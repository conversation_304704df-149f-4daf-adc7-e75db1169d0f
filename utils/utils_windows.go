//go:build !windows
// +build !windows

package utils

import (
	"os"
	"syscall"
)

func (u *Utils) HideFile(filename string) error {
	filenameW, err := syscall.UTF16PtrFromString(filename)
	if err != nil {
		return err
	}
	err = syscall.SetFileAttributes(filenameW, syscall.FILE_ATTRIBUTE_HIDDEN)
	if err != nil {
		return err
	}
	return nil
}

func (u *Utils) CreateHiddenFolder(foldername string) error {
	err := os.Mkdir(foldername, 0755) // 创建文件夹
	if err != nil {
		return err
	}
	// // syscall
	// 	// 将文件夹设置为隐藏属性
	err = syscall.SetFileAttributes(foldername, syscall.FILE_ATTRIBUTE_HIDDEN)
	if err != nil {
		return err
	}

	return nil
}
