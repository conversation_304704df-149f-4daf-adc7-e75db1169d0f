package utils

import (
	"context"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"path/filepath"
)

func NewUtil() *Utils {
	return &Utils{}
}

type Utils struct {
	ctx context.Context
}

func (u *Utils) ChangeSystemName(title string) string {
	newTitle := []byte(fmt.Sprintf(`export const SYSTEMNAME = "%s"`, title))

	// // 读取新的文件
	err := os.WriteFile("oem.js", newTitle, 0644)
	if err != nil {

		return fmt.Sprintf("%s", err)
	}
	return "ok"

}

func (u *Utils) CreateHiddenFolder(foldername string) string {
	err := os.Mkdir("."+foldername, 0755) // 创建文件夹
	if err != nil {
		return "fail"
	}

	return "ok"
}

func (u *Utils) ReplaceLogo(url string) error {
	// 读取新的图片文件
	newImageFile, err := os.Open("new_logo.png")
	if err != nil {
		log.Fatal(err)
	}
	defer newImageFile.Close()

	// 读取新图片的数据
	newImageData, err := io.ReadAll(newImageFile)
	if err != nil {
		log.Fatal(err)
	}

	// 获取 logo.png 的绝对路径
	logoPath, err := filepath.Abs("./frontend/src/assets/image/logo.png")
	if err != nil {
		return err
	}

	// 写入新的图片数据到 logo.png
	err = os.WriteFile(logoPath, newImageData, 0644)
	if err != nil {
		return err
	}

	log.Println("Logo replaced successfully")
	return nil
}

func (u *Utils) GetLocalIp() string {
	// 获取本机所有的网络接口信息
	var Ip = ""
	// 获取所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		fmt.Println("Failed to retrieve network interfaces: ", err)
		return Ip
	}

	// 遍历所有接口
	for _, iface := range interfaces {
		// 检查接口是否激活
		if iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 检查接口是否为回环接口
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 获取接口的IP地址信息
		addrs, err := iface.Addrs()
		if err != nil {
			fmt.Println("Failed to retrieve addresses for interface: ", err)
			continue
		}

		// 遍历所有地址
		for _, addr := range addrs {
			// 确保地址是IPv4类型

			ipnet, ok := addr.(*net.IPNet)
			if !ok {
				continue
			}

			// 确保IP不是回环地址
			if ipnet.IP.IsLoopback() || ipnet.IP.IsLinkLocalMulticast() || ipnet.IP.IsLinkLocalUnicast() {
				continue
			}
			parsedIP := net.ParseIP(ipnet.IP.String())
			// 如果解析成功并且是IPv4地址，则返回true
			if parsedIP != nil && parsedIP.To4() != nil {

				Ip = ipnet.IP.String()
			}
			// 输出IPv4地址

		}
	}
	return string(Ip)
}
