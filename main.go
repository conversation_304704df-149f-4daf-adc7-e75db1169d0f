package main

import (
	"embed"

	"changeme/utils"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// Create an instance of the app structure
	app := NewApp()
	utils := utils.NewUtil()

	// Create application with options
	err := wails.Run(&options.App{
		Title:         "全自动疫苗智能传输系统",
		WindowStartState: options.Maximised,
		DisableResize: false,
		Frameless:     true,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
			utils,
		},
	})



	if err != nil {
		println("Error:", err.Error())
	}
}
