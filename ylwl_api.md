# Domore智慧云平台开放API使用手册（V5.20.1）

## 版本说明

| 版本 | 内容变更 | 变更人 | 变更时间 | 说明 |
| :--- | :--- | :--- | :--- | :--- |
| V3.0 | 完成V3文档 | 后端组 | | |
| V3.0 | 新增根据设备mac查询绑定关系接口 | 后端组 | | |
| V3.0 | 根据ID删除网关,改为支持ID或mac删除 | 后端组 | 2021-12-16 | |
| V3.0 | 新增修改门店接口，开启\关闭门店接口，根据标签批量唤醒设备接口 | 后端组 | 2021-12-16 | |
| V3.0 | 修改标签绑定的数据及模板的绑定关系（刷图，支持亮灯和熄灯）中支持图片字段传base64 | 后端组 | 2021-12-17 | |
| V3.0 | 运维账号---根据设备MAC查询设备 | 后端组 | 2022-01-10 | 版本3.10.0 |
| V3.0 | 支持重要数据接口的签名和验签 | 后端组 | 2022-2-25 | 版本3.12.0 |
| V3.0 | 新增警示灯标签批量绑定和解绑，警示灯灭灯接口 | 后端组 | 2022-6-21 | |
| V5 | 删除标签和数据的绑定关系 | 后端组 | 2022-7-25 | |
| V5 | 批量修改数据刷图（包含了亮灯和熄灯关联警示灯） | 后端组 | 2022-10-12 | |
| V5 | 获取APP蓝牙刷图数据 | 后端组 | 2022-10-17 | |
| V5 | 商户主账号维护管理接口 | 后端组 | 2023-8-10 | |
| V5 | 导入设备白名单接口，警示灯标签绑定关系导出接口 | 后端组 | 2024-3-27 | 版本5.13.0 |
| V5 | 新增标签绑定：一签多用接口；更新接口排序。 | 后端组 | 2024-10-29 | 版本5.17.0 |
| V5 | 优化：批量MAC查询绑定关系； | 后端组 | 2024-11-12 | 版本5.17.1 |

---

# 目录
- [登录接口](#登录接口)
  - [1. 登录接口](#1-登录接口)
- [应用场景接口](#应用场景接口)
  - [2. 添加应用场景](#2-添加应用场景)
  - [3. 修改应用场景](#3-修改应用场景)
  - [4. 关闭（开启）应用场景](#4-关闭开启应用场景)
  - [5. 获取预警信息](#5-获取预警信息)
  - [6. 获取用户应用场景信息](#6-获取用户应用场景信息)
  - [7. 获取场景内操作日志信息](#7-获取场景内操作日志信息)
- [刷图数据接口](#刷图数据接口)
  - [8. 查询动态字段](#8-查询动态字段)
  - [9. 批量:新增/修改数据、开/关灯、刷图](#9-批量新增修改数据开关灯刷图)
  - [10. 批量删除数据信息](#10-批量删除数据信息)
  - [11. 系统数据导入到指定区域](#11-系统数据导入到指定区域)
  - [12. 获取数据信息](#12-获取数据信息)
  - [13. 数据iD查询绑定标签及其所属门店](#13-数据id查询绑定标签及其所属门店)
- [模板接口](#模板接口)
  - [14. 查询用户模板列表](#14-查询用户模板列表)
  - [15. 查询已绑定数据的模板预览图](#15-查询已绑定数据的模板预览图)
- [网关接口](#网关接口)
  - [16. 添加网关](#16-添加网关)
  - [17. 删除网关](#17-删除网关)
  - [18. 查询网关列表](#18-查询网关列表)
  - [19. 修改网关信息](#19-修改网关信息)
- [设备接口](#设备接口)
  - [20. 批量添加设备](#20-批量添加设备)
  - [21. 根据标签Mac批量删除](#21-根据标签mac批量删除)
  - [22. 获取设备列表](#22-获取设备列表)
  - [23. 标签控制亮灯](#23-标签控制亮灯)
  - [24. 标签批量刷图](#24-标签批量刷图)
  - [25. 标签绑定：绑定与修改数据及模板](#25-标签绑定绑定与修改数据及模板)
  - [26. 标签绑定：一签多用](#26-标签绑定一签多用)
  - [27. 标签解绑：标签/数据/模板的绑定关系](#27-标签解绑标签数据模板的绑定关系)
  - [28. 修改标签的备注信息](#28-修改标签的备注信息)
  - [29. 根据标签MAC批量查询绑定的数据iD与模板](#29-根据标签mac批量查询绑定的数据id与模板)
  - [30. 根据标签Mac批量唤醒](#30-根据标签mac批量唤醒)
  - [31. 根据标签MAC查询设备所在用户/区域](#31-根据标签mac查询设备所在用户区域)
  - [32. 警示灯批量绑定标签](#32-警示灯批量绑定标签)
  - [33. 导入设备白名单](#33-导入设备白名单)

---

# 登录接口

## 1. 登录接口
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/action/login`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 用账号，和加密后的密码进行登录

- **请求参数**


| 字段名 | 变量名 | 数据类型 | 必填 | 长度 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 用户名 | `username` | String | true | 30 | | Body |
| 密码 | `password` | String | true | | 原密码32位小写MD5加密 | Body |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/action/login
  {
      "username": "admin",
      "password": "fd5c1cf04c8e68ded43719f50fa81fda"
  }
  ```

- **响应参数**


| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `msg` | String | 成功/失败 |
| 场景有无 | `hasScene` | boolean | 是否有场景，如零售 |
| token验证 | `token` | String | 认证凭证token，有效期24H |
| 身份认证 | `licenseGrant` | boolean | |
| 新手指引 | `displayGuide` | boolean | |
| 响应数据 | `data` | String | 响应数据 |
| 用户信息 | `userInfo` | Object | 用户信息 |
| 用户id | `id` | String | |
| 商户编码 | `merchantCode` | String | |
| 电话号码 | `mobile` | String | |
| 用户标识 | `flag` | Integer | 0商户普通用户，1商户主账号，5云里物里运维管理账号,默认是0 |
| 是否是管理员| `admin` | boolean | |
| 邮箱 | `email` | String | |

- **错误码列表**


| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 10024 | 密码不可以为空 |
| 10025 | 您输入的登录账号不存在 |
| 10026 | 您输入的密码不对 |

- **响应示例**
  ```json
  {
      "code": 200,
      "msg": "成功",
      "data": {
          "userInfo": "{\"admin\":false,\"autoPassWord\":\"\",\"condition\":\"\",\"confirmPassword\":\"\",\"count\":null,\"createTime\":*************,\"createdBy\":\"0\",\"definePassWord\":\"\",\"description\":\"MasterAccount\",\"email\":\"\",\"flag\":\"1\",\"guideDisplay\":0,\"guideTime\":null,\"id\":\"1325609281856212992\",\"image\":\"\",\"loginTime\":null,\"loginname\":\"胡某\",\"merchantCode\":\"886092\",\"mobile\":\"***********\",\"name\":\"胡某\",\"nickname\":\"\",\"organizationNameList\":[],\"password\":\"\",\"resetPassword\":null,\"roleNameList\":[],\"salt\":\"null\",\"status\":0,\"updateTime\":null,\"updatedBy\":\"\"}",
          "hasScene": true,
          "token": "eyJhbGciOiJSUzUxMiJ9.**************************************************************.pyxprkOIePRuzYbDRX1sCYBxLTVA6jdboW5It3Z6j8-IsnFZyvrFekVCbOX9QYvMIqtowa-mgNu6XbAPlStslEMkrMSB8i3BjOuJ8HAV3vou1cAPAVbfL94735jf_WhGk0PrZAfH8ovTg-rygPrqfx1AkiJwKer5WYEDHp_tRsU",
          "displayGuide": true
      }
  }
  ```

---

# 应用场景接口

## 2. 添加应用场景
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/store/add`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 应用场景添加：门店、仓库、会议室等区域管理

- **请求参数**


| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 门店编号 | `number` | String | true | 值不能重复，唯一 | Body |
| 门店名 | `name` | String | true | | Body |
| 门店地址 | `address` | String | true | | Body |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/store/add
  {
      "number": "003",
      "name": "roket",
      "address": "ali"
  }
  ```

- **响应参数**


| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `message` | String | 提示信息 |
| 数据 | `data` | json串 | 添加成功,data对象包含门店ID |

- **错误码列表**


| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 10059 | 没有权限 |
| 12053 | 获取登录用户信息失败 |
| 12052 | 此门店编号已添加 |
| 12100 | 门店名已添加 |
| 54029 | 门店编号、名称，地址任何一个都不能为空 |
| 54030 | 门店编号只能数字 |

- **响应示例**
  ```json
  {
      "code": 200,
      "msg": "成功",
      "data": {
          "storeId": "1425028090339794944"
      }
  }
  ```

## 3. 修改应用场景
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/store/update`
  - `请求方式`: `PUT`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 修改门店、仓库、会议室等区域信息

- **请求参数**


| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 是否激活 | `active` | Integer | true | 是否是处于激活状态的门店，1：激活，0关闭 | Body |
| 门店id | `id` | String | true | | Body |
| 门店名 | `name` | String | true | | Body |
| 门店地址 | `address` | String | true | | Body |
| 门店编号 | `number` | String | true | | Body |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/store/update
  {
      "id": "1470218173264433152",
      "name": "一号门店",
      "address": "港之龙",
      "active": 1,
      "number": "12345"
  }
  ```

- **响应参数**


| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `msg` | String | 提示信息 |
| 数据 | `data` | json串 | 修改成功,null |

- **错误码列表**


| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 12004 | 缺少请求参数 |
| 12036 | 修改失败 |
| 54042 | 门店编号不允许修改 |

- **响应示例**
  ```json
  {
      "code": 200,
      "msg": "成功",
      "data": null
  }
  ```

## 4. 关闭（开启）应用场景
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/store/openOrClose`
  - `请求方式`: `GET`
  - `接口说明`: 关闭或开启门店、仓库、会议室等区域管理

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 激活状态 | `active` | String | true | 0：关闭门店，1：开启门店 | Param |
| 门店ID | `storeId` | String | true | | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/store/openOrClose?storeId=1470218173264433152&active=1
  ```

- **响应参数**

| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `msg` | String | 提示信息 |
| 数据 | `data` | json串 | 添加成功,null |

- **错误码列表**

| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 12004 | 参数不对，请检查 |
| 12005 | 操作失败，请检查网络 |
| 12114 | 非此商户门店 |

- **响应示例**
  ```json
  {
      "code": 200,
      "msg": "成功",
      "data": null
  }
  ```

## 5. 获取预警信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/warning/findAllWarnings`
  - `请求方式`: `GET`
  - `接口说明`: 获取门店、仓库、会议室等区域内预警信息

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 预警信息类型 | `screening` | String | false | brush：刷图警告，upgrade：升级警告 | Param |
| 门店ID | `storeId` | String | true | | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/warning/findAllWarnings?screening=brush&storeId=1328266049345687552
  ```

- **响应参数**

| 字段名 | 变量名 | 数据类型 |
| :--- | :--- | :--- |
| 状态码 | `code` | Integer |
| 提示信息 | `page` | String |
| 页尺寸 | `size` | Integer |
| 总页数 | `totalPage` | Integer |
| 总数量 | `totalCount` | Integer |
| 行记录 | `rows` | Array |
| 警告id | `id` | String |
| 设备mac | `mac` | String |
| 网关mac | `gatewayMac` | String |
| 标签秘钥 | `key` | String |
| 屏幕尺寸 | `screenSize` | Integer |
| 图像数据 | `image` | String |
| 图像id | `imageId` | Integer |
| 门店id | `storeId` | String |
| 操作人 | `operator` | String |
| 数据id | `commodity` | String |
| 标签当前版本 | `currentVersion` | String |
| 当前要升级版本 | `upgradeVersion` | String |
| 标签升级包id | `labelpackId` | String |
| 数据 | `data` | String |
| 操作 | `operation` | String |
| 操作码 | `opCode` | Integer |
| token | `token` | String |
| 文件大小 | `fileSize` | Integer |
| Url大小 | `urlSize` | Integer |
| Url | `url` | String |
| 绑定失败 | `bingingFailure` | Integer |
| 绑定成功 | `bingingTotal` | Integer |
| 创建时间 | `createTime` | Date |
| 商户编号 | `merchantCode` | String |
| 警告表示 | `flag` | Integer |
| 序列号 | `serialNumber` | String |

- **错误码列表**

| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 10059 | 没有权限 |
| 12053 | 获取登录用户信息失败 |
| 12052 | 此门店编号已添加 |
| 12100 | 门店名已添加 |

- **响应示例**
  ```json
  {
      "code": null,
      "page": null,
      "size": null,
      "totalPage": null,
      "totalCount": null,
      "rows": [
          {
              "id": "60d59e80fa770c44f5de7acb",
              "mac": "ac233fd010ac",
              "gatewayMac": "AC233FC08FBF",
              "key": "3141592653589793",
              "screenSize": 3,
              "image": "",
              "imageId": 1624612250,
              "storeId": "1408232104776437760",
              "operator": "SystemRepeatPushJob",
              "commodity": "1",
              "currentVersion": null,
              "upgradeVersion": null,
              "labelpackId": null,
              "data": null,
              "operation": "brush",
              "opCode": null,
              "token": "",
              "fileSize": 9526,
              "urlSize": 0,
              "url": "",
              "bingingFailure": 0,
              "bingingTotal": 0,
              "createTime": "2021-06-2517:14:40",
              "merchantCode": "304748",
              "flag": 0,
              "serialNumber": 1624612250
          }
      ]
  }
  ```

## 6. 获取用户应用场景信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/store/list`
  - `请求方式`: `GET`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 获取用户门店、仓库、会议室等区域信息

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 是否激活 | `active` | Integer | true | 是否是处于激活状态的门店，1：激活，0关闭 | Param |
| 模糊查询条件 | `condition` | String | false | 可以是门店的名称，编号，地址中任意一个值 | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/store/list?active=1
  ```

- **响应参数**

| 字段名 | 变量名 | 数据类型 |
| :--- | :--- | :--- |
| 状态码 | `code` | Integer |
| 提示信息 | `message` | String |
| 响应数据 | `data` | Object |
| 门店id | `id` | String |
| 门店自定义id | `number` | String |
| 门店名 | `name` | String |
| 激活状态 | `active` | Integer |
| 国家 | `country` | String |
| 省份 | `province` | String |
| 城市 | `city` | String |
| 地址 | `address` | String |
| 服务器地址 | `serverIp` | String |
| 图片 | `image` | String |
| 图片全路径 | `imageFullPath` | String |
| 描述 | `description` | String |
| 经度 | `latitude` | String |
| 纬度 | `longtitude` | String |
| 创建时间 | `createTime` | Date |
| 修改时间 | `updateTime` | Date |
| 创建人 | `create_by` | String |
| 修改人 | `update_by` | String |
| 商户编码 | `merchant_code` | String |
| 标记 | `remark` | String |
| 删除标志 | `delete_flag` | Integer |

- **响应示例**
  ```json
  {
      "code": 200,
      "msg": "成功",
      "data": [
          {
              "id": "1341258324526391296",
              "uuid": null,
              "name": "hx",
              "active": 1,
              "country": null,
              "province": null,
              "city": null,
              "address": "清湖地铁站",
              "serverIp": "",
              "image": "",
              "imageFullPath": null,
              "description": null,
              "latitude": null,
              "longitude": null,
              "createTime": "2020-12-2213:44:39",
              "updateTime": "2020-12-2213:44:39",
              "createBy": "1325609281856212992",
              "updateBy": null,
              "merchantCode": "886092",
              "remark": null,
              "deleteFlag": 0
          }
      ]
  }
  ```

## 7. 获取场景内操作日志信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/logs/queryList`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 获取门店、仓库、会议室等区域内操作日志信息

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 条件对象 | `operateLogReqVo` | Object | true | 此对象包含以下内容，放在body里面 | Body |
| 模糊查询条件 | `condition` | String | false | 模糊标签的mac | operateLogReqVo |
| 动作类型 | `actionType` | String | false | 1为刷图，2为升级 | operateLogReqVo |
| 页码 | `currentPage` | Int | true | 查询第几页 | operateLogReqVo |
| 页面大小 | `pageSize` | Int | true | 每页多少条 | operateLogReqVo |
| 设备类型 | `objectType` | String | true | 1为标签5为警示灯 | operateLogReqVo |
| 门店id | `storeId` | String | true | 门店id | operateLogReqVo |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/logs/queryList
  {
      "condition": "ac233fd35bee",
      "actionType": "1",
      "currentPage": "1",
      "pageSize": "10",
      "objectType": "1",
      "storeId": "1447823804414824448"
  }
  ```

- **响应参数**

| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `msg` | String | 提示信息 |
| 当前页码 | `currentPage` | Integer | 当前页码 |
| 页面大小 | `pageSize` | Integer | 页面大小 |
| 总数据数量 | `totalNum` | Integer | 总数据数量 |
| 是否有下一页 | `isMore` | Integer | 1为有下一页 |
| 总页码数量 | `totalPage` | Integer | totalPage |
| 开始索引 | `startIndex` | Integer | |
| 数据集合 | `items` | List | |
| 操作人 | `operator` | String | |
| 更新时间 | `createTime` | String | yyyy-MM-dd HH-mm-ss |
| 操作类型 | `actionType` | String | 1刷图2升级3新增4删除5绑定6预约下发7警示灯 |
| 操作结果 | `result` | String | 1更新成功 2更新失败 3秘钥错误 ... |

- **响应示例**
  ```json
  {
    "code": 200,
    "msg": "成功",
    "currentPage": 1,
    "pageSize": 10,
    "totalNum": 1,
    "isMore": 0,
    "totalPage": 1,
    "startIndex": 0,
    "items": [
      {
        "id": "1727931195527794688",
        "operator": "王志峰",
        "commodity": "351234567890",
        "labelMac": "c203030012cc",
        "result": "1",
        "objectType": "1",
        "actionType": "1",
        "storeId": "1447823804414824448",
        "createTime": "2023-11-2414:04:35",
        "merchantCode": "902342",
        "gatewayMac": "AC233FC0B08E",
        "opCode": 1700805842,
        "goods": {
          "id": "351234567890",
          "storeId": "1447823804414824448",
          "name": "深圳云里物里科技股份有限公司",
          "code": "351234567890",
          "jiage": "88.0",
          "cuxiaojia": "78.5",
          "huiyuanjia": "80.5",
          "hysj": "2023/09/15",
          "yuyueriqi": "2023/09/15",
          "shuliang": "129988",
          "theme": "731234567890",
          "Reservation": "型号：DS035-黑白红黄4色屏,规格：3.5吋,99.5*49.5*9.8mm，电池型号：CP502440/1200mAH续航时间:10年(每天刷新3次)"
        }
      }
    ]
  }
  ```

---

# 刷图数据接口

## 8. 查询动态字段
- **接口说明**: 刷图数据类自3.0版本后类除基础字段外其他字段皆为自定义的动态字段...
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/scene/findDongTaiZiDuan`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 查询刷图数据的自定义动态字段

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/scene/findDongTaiZiDuan
  ```

- **响应参数**

| 字段名 | 变量名 | 数据类型 | 备注 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | Integer | 详情见接口错误码 |
| 提示信息 | `message` | String | 提示信息 |
| 响应数据 | `data` | Object | 响应数据对象 |
| 数据ID | `_id` | String | 字段对象的ID |
| 字段英文名 | `id` | String | 字段英文名 |
| 序号 | `number` | Integer | |
| 中文名称 | `name` | String | |
| 字段描述 | `description` | String | |
| 字段类型 | `colunmDataType` | Integer | 0:纯文本;1:数字;2:条形码... |
| 商户编码 | `merchantCode` | String | |
| 字段前缀 | `prefix` | String | |
| 创建时间 | `createTime` | Date | |
| 创建人ID | `createdBy` | String | |
| 更新时间 | `updateTime` | Date | |
| 更新操作人 | `updatedBy` | String | |

- **错误码列表**

| 状态码 | 描述 |
| :--- | :--- |
| 200 | 成功 |
| 10059 | 没有权限 |
| 12051 | 门店id不能为空 |
| 12030 | 删除失败 |
| 12011 | 此数据已添加 |

- **响应示例**
  ```json
  {
    "code": 200,
    "msg": "成功",
    "data": [
      {
        "_id": "60af6b554149c400017c9527",
        "id": "id",
        "number": 10001,
        "colunmDataType": 7,
        "name": "id",
        "merchantCode": "878950",
        "createTime": "2021-05-2718:35:12",
        "createdBy": "1397852390207524864",
        "updateTime": "2021-05-2718:35:12",
        "updatedBy": "1397852390207524864"
      }
    ]
  }
  ```

## 9. 批量:新增/修改数据、开/关灯、刷图
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/goods/batchUpdateBrushLed`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 修改刷图数据信息并刷图，点灯灭灯，修改不存在的数据，就新增此数据到平台（关联警示灯）

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | 登录接口返回的token值 | Header |
| 刷图数据集合 | `goodsList` | List | true | 刷图数据集合 | Body |
| 门店id | `storeId` | String | true | 门店id | Body |
| 亮灯颜色 | `color` | Integer | false | 0:关闭, 1:蓝色, 2:绿色, 3:红色... | Body |
| 操作总时长 | `total` | Integer | false | 单位s（点灯必传） | Body |
| 单周期内的亮灯时间 | `period` | Integer | false | 单位ms（点灯必传） | Body |
| 单周期时间 | `interval` | Integer | false | 单位ms（点灯必传） | Body |
| 亮度 | `brightness` | Integer | false | 1-100（点灯必传） | Body |
| 操作码 | `opCode` | Integer | false | 1000000000-2147483647 | Body |

- **请求示例**
  ```json
  {
    "goodsList": [
      {
        "id": "1",
        "storeId": "1408232104776437760",
        "name": "春江花月夜",
        "author": "张若虚",
        "dynasty": "唐代"
      }
    ],
    "storeId": "1408232104776437760",
    "total": 15,
    "period": 100,
    "interval": 900,
    "brightness": 100,
    "color": 1
  }
  ```

## 10. 批量删除数据信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/goods/batchDelete`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 批量删除刷图数据

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 门店ID | `storeId` | String | true | Body |
| 刷图数据ID数组 | `idArray` | Array | true | Body |

- **请求示例**
  ```json
  {
    "idArray": ["3", "4"],
    "storeId": "1408232104776437760"
  }
  ```

## 11. 系统数据导入到指定区域
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/system/goods/import`
  - `请求方式`: `POST`
  - `内容类型`: `multipart/form-data`
  - `接口说明`: 导入模板文件，触发刷图。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | header |
| 门店ID列表 | `storeIdList` | List | true | Param |
| 导入文件 | `file` | form | true | Body |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/system/goods/import?storeIdList=1422849708086267905,1425032158484893696
  ```

## 12. 获取数据信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/goods/singleQuery`
  - `请求方式`: `GET`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 获取区域内所有数据信息。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 门店ID | `storeId` | String | true | Param |
| 数据ID | `id` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/goods/singleQuery?storeId=1909251489541394432&id=1231
  ```

## 13. 数据id查询绑定标签及其所属门店
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/goods/findByGoodsId`
  - `请求方式`: `GET`
  - `接口说明`: 根据刷图数据ID查询绑定标签及其所属门店。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 刷图数据id | `goodsId` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/goods/findByGoodsId?goodsId=6901939721248
  ```

---

# 模板接口

## 14. 查询用户模板列表
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/template/findAll`
  - `请求方式`: `GET`
  - `接口说明`: 查询模板信息。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 当前页 | `page` | Integer | true | | Param |
| 每页数 | `size` | Integer | true | | Param |
| 搜索关键词 | `fuzzy` | String | false | | Param |
| 颜色 | `color` | String | false | | Param |
| 尺寸 | `inch` | double | false | | Param |
| 门店id | `storeId` | String | true | | Param |
| 筛选 | `screening` | Integer | false | 0:所有, 1:系统, 其他:门店 | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/template/findAll?page=1&size=6&storeId=1341258324526391296&screening=2&inch=0&color&fuzzy
  ```

## 15. 查询已绑定数据的模板预览图
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/template/preview`
  - `请求方式`: `POST`
  - `接口说明`: 返回base64格式的预览图。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 模板名 | `demoName` | String | true | Param |
| 数据id | `id` | String | true | Param |
| 门店id | `storeId` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/template/preview?demoName=2.13黑白模板&id=6902265114420&storeId=1430466345245347840
  ```

---

# 网关接口

## 16. 添加网关
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/gateway/add`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 添加网关。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 网关mac地址 | `mac` | String | true | Body |
| 网关名称 | `name` | String | true | Body |
| 门店ID | `storeId` | String | true | Body |

- **请求示例**
  ```json
  {
    "mac": "AC233FC03CEC",
    "name": "GW-AC233FC03CEC",
    "storeId": "1328266049345687552"
  }
  ```

## 17. 删除网关
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/gateway/delete`
  - `请求方式`: `GET`
  - `接口说明`: 删除网关。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 网关ID | `id` | String | true | 值可以是id或网关mac | Param |
| 门店ID | `storeId` | String | true | | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/gateway/delete?id=1349244935877955584&storeId=1328266049345687552
  ```

## 18. 查询网关列表
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/gateway/listPage`
  - `请求方式`: `GET`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 查询对应门店下的网关信息。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 门店ID | `storeId` | String | true | Param |
| 页数 | `page` | Integer | true | Param |
| 每页数量 | `size` | Integer | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/gateway/listPage?page=1&size=10&storeId=1326065100695539712
  ```

## 19. 修改网关信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/gateway/update`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 修改对应门店下的网关信息。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 网关Id | `id` | String | true | 值可以是id或网关mac | Body |
| 网关名称 | `name` | String | true | | Body |

- **请求示例**
  ```json
  {
    "id": "1339854807833251840",
    "name": "AC233FC03D511"
  }
  ```

---

# 设备接口

## 20. 批量添加设备
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/batchAdd`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 批量新增标签。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 门店ID | `storeId` | String | true | | Body |
| 标签mac地址数组 | `macArray` | Array | true | string类型数组 | Body |
| 设备类型 | `type` | Integer | true | 1:标签, 2:大灯 | Body |

- **请求示例**
  ```json
  {
    "storeId": "1588041156676",
    "macArray": ["ac233fd01335", "ac233fd01336"],
    "type": 1
  }
  ```

## 21. 根据标签Mac批量删除
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/batchDeleteLabels`
  - `请求方式`: `POST`
  - `内容类型`: `application/json`
  - `接口说明`: 根据设备mac批量删除设备。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 门店ID | `storeId` | String | true | Body |
| 设备mac集合 | `macs` | List | true | Body |

- **请求示例**
  ```json
  {
    "macs": ["ac233fd136e0", "ac233fd15376"],
    "storeId": "1447823804414824448"
  }
  ```

## 22. 获取设备列表
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/cascadQuery`
  - `请求方式`: `GET`
  - `接口说明`: 查询标签列表。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 当前页 | `page` | Integer | true | | Param |
| 页尺寸 | `size` | Integer | true | | Param |
| 门店id | `storeId` | String | true | | Param |
| 标签模糊搜索 | `fuzzy` | String | false | | Param |
| 标签的状态 | `eqstatus` | List | false | 1:离线, 2:在线, 8:已绑定, 9:未绑定, 5:低电量 | Param |
| 设备类型 | `type` | Integer | true | 1:标签, 2:警示灯 | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/label/cascadQuery?page=1&size=10&storeId=1328266049345687552&eqstatus=2,3&type=1
  ```

## 23. 标签控制亮灯
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/led`
  - `请求方式`: `GET`
  - `接口说明`: 通过设备mac地址点灯。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 标签mac地址 | `mac` | String | true | | Param |
| 门店id | `storeId` | String | true | | Param |
| 亮灯颜色 | `color` | Integer | true | 0-7: 关闭,蓝,绿,红,黄,白,品红,青色 | Param |
| 亮灯总周期数 | `total` | Integer | true | | Param |
| 亮灯时长 | `period` | Integer | true | ms | Param |
| 灭灯时长 | `interval` | Integer | true | ms | Param |
| 灯的亮度 | `brightness` | Integer | true | 1-100 | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/label/led?storeId=1341258324526391296&color=1&total=6&period=500&interval=900&brightness=100&mac=ac233fd00fe0
  ```

## 24. 标签批量刷图
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/batchBrush`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 批量刷图。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | header |
| 门店ID | `storeId` | String | true | Body |
| 设备mac地址数组 | `macs` | Array | true | Body |

- **请求示例**
  ```json
  {
    "macs": ["ac233fd01335", "ac233fd00708"],
    "storeId": "1408232104776437760"
  }
  ```

## 25. 标签绑定：绑定与修改数据及模板
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/updateBindBrush`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 修改标签绑定的数据及模板（会执行刷图）。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | header |
| 标签mac地址 | `labelMac` | String | true | | Body |
| 门店ID | `storeId` | String | true | | Body |
| 绑定的数据 | `goodsMap` | Map | true | | body |
| 模板id | `demoIdMap` | Map | true | key=A:正面, key=B:反面 | Body |
| ...亮灯参数 | | | | (同上) | Body |

- **请求示例**
  ```json
  {
    "labelMac": "ac233fd35bee",
    "storeId": "1447823804414824448",
    "goodsMap": {
      "id": "73123456",
      "name": "大白菜",
      "base64": "data:image/png;base64,..."
    },
    "demoIdMap": {
      "A": "1595983752910082048",
      "B": "1465936650465972225"
    },
    "opCode": 1,
    "brightness": 100,
    "color": 1,
    "interval": 900,
    "period": 500,
    "total": 120
  }
  ```

## 26. 标签绑定：一签多用
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/bindMultiData`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 一个标签可以绑定多条数据显示。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 标签Mac | `labelMac` | String | true | Body |
| 门店ID | `storeId` | String | true | Body |
| 绑定数据ID | `goodsIds` | String[] | true | Body |
| 多数据模板ID | `demoIdMap` | Map | true | Body |

- **请求示例**
  ```json
  {
    "labelMac": "ac233fd0e957",
    "storeId": "1447823804414824448",
    "goodsIds": ["6901285991240", "6933179260170"],
    "demoIdMap": {
      "A": "1856183199449747456"
    }
  }
  ```

## 27. 标签解绑：标签/数据/模板的绑定关系
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/deleteBind`
  - `请求方式`: `POST`
  - `接口说明`: 删除标签和数据及模板的绑定关系。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| mac地址 | `mac` | String | true | Param |
| 门店id | `storeId` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/label/deleteBind?mac=ac233fd90007&storeId=1549629697900417024
  ```

## 28. 修改标签的备注信息
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/updateRemark`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 添加标签设备的备注。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | header |
| 标签mac | `mac` | String | true | Body |
| 备注信息 | `remark` | String | true | Body |
| 门店ID | `storeId` | String | true | Body |

- **请求示例**
  ```json
  {
    "mac": "ac233fd136e0",
    "remark": "日常生活用品区",
    "storeId": "1427509585467412481"
  }
  ```

## 29. 根据标签MAC批量查询绑定的数据iD与模板
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/batchBindQuery`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 批量查询标签的绑定关系。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| Mac集合 | `macs` | List | true | | Body |
| 门店id | `storeId` | String | true | | Body |
| 数据ID集合 | `goodIds` | List | false | Mac集合和数据ID集合二选一 | Body |

- **请求示例**
  ```json
  {
    "macs": ["a00ee2300030", "ac233fd15340"],
    "storeId": "1813398695832457216"
  }
  ```

## 30. 根据标签Mac批量唤醒
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/batchWake`
  - `请求方式`: `POST`
  - `接口说明`: 根据标签Mac批量唤醒设备。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 标签mac | `macList` | List | true | Body |
| 门店id | `storeId` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/label/batchWake?storeId=1470218173264433152
  ["ac233fd01107", "ac233fd01108"]
  ```

## 31. 根据标签MAC查询设备所在用户/区域
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/merchant/queryDeviceByMac`
  - `请求方式`: `GET`
  - `接口说明`: 只有运维账号admin才有这个功能。

- **请求参数**

| 字段名 | 变量名 | 数据类型 | 必填 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | Header |
| 设备mac | `mac` | String | true | Param |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/merchant/queryDeviceByMac?mac=ac230fd28a91
  ```

## 32. 警示灯批量绑定标签
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/label/warningLightLabel/save`
  - `请求方式`: `POST`
  - `接口说明`: 根据警示灯mac和标签Mac批量绑定。

- **请求参数**


| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | | Header |
| 标签mac集合 | `labelMacList` | List | true | 空集合时执行解绑 | Body |
| 门店id | `storeId` | String | true | | Body |
| 警示灯mac | `warningLightMac` | String | true | | Body |

- **请求示例**
  ```json
  {
    "labelMacList": ["ac233ff00dd", "ac235422465"],
    "storeId": "1470218173264433152",
    "warningLightMac": "ac23544224745"
  }
  ```

## 33. 导入设备白名单
- **接口定义**
  - `URL`: `https://cloud.ylwlesl.com/apis/esl/operationlabel/importLabelByJson`
  - `请求方式`: `POST`
  - `内容类型`: `application/json;charset=utf-8`
  - `接口说明`: 导入设备白名单（需要admin账号）。

- **请求参数**


| 字段名 | 变量名 | 数据类型 | 必填 | 备注 | 参数位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 授权token | `token` | String | true | admin账号登录 | Header |
| 设备类型 | `type` | Integer | true | 1:标签, 2:警示灯, 4:雷达... | Param |
| 设备mac和密钥集合 | `labelList` | List | true | | Body |
| 设备mac | `mac` | String | true | 在labelList内 | |
| 密钥 | `activekey` | String | true | 在labelList内 | |

- **请求示例**
  ```
  https://cloud.ylwlesl.com/apis/esl/operationlabel/importLabelByJson?type=1
  [
    {
      "mac": "c3000023b1ba",
      "activekey": "3f9c011243354c0c"
    },
    {
      "mac": "123456",
      "activekey": "123456"
    }
  ]