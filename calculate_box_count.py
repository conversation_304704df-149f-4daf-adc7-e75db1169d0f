def calculate_box_count(measured_height, initial_height=1033, initial_top_height=129, initial_box_count=47, removed_box_count=15, remaining_top_height=377, actual_remaining_box_count=32):
    # 初始盒子高度总和
    H_initial = initial_height - initial_top_height

    # 每个盒子的平均高度
    h_average = H_initial / initial_box_count

    # 拿走15盒后的盒子高度总和
    H_after = initial_height - remaining_top_height

    # 剩余盒子的平均高度
    h_remaining = H_after / actual_remaining_box_count

    # 压缩系数
    compression_factor = h_average / h_remaining

    # 实际盒子数量
    N = (initial_height - measured_height) / (h_average * compression_factor)

    return round(N)

# 示例使用
if __name__ == "__main__":
    measured_height = 377  # 测量的物体上空高度
    box_count = calculate_box_count(measured_height)
    print(f"计算出的盒子数量: {box_count}")