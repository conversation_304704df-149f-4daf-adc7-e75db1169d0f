# 快速开始

# - 增加 CI 配置文件：
# 1. 在项目根目录中增加 .coding-ci.yml 配置文件。
# 2. 其描述了当仓库发生一些事件时，Coding-CI 应该如何去进行处理。
# 3. 将配置文件 push 到远程 master

# 想了解配置文件更多用法请移步 【配置文件】（https://ci.coding.net/docs/configuration.html）

master:
  push:
    - stages:
      - name: echo
        script: echo "hello world"
      - name: echo2
        script: echo "hello world2"


# 这个案例描述的流程如下：

# 1. 声明了在 master 分支在收到 push 事件时（即有新的 Commit 推送到 master 分支）的时候
# 2. 依次执行任务 echo echo2 的script
