{
    "compilerOptions": {
        "target": "ESNext",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "moduleResolution": "Node",
        "strict": false,
        "jsx": "preserve",
        "sourceMap": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "esModuleInterop": true,
        "lib": ["ESNext", "DOM"],
        // 跳过库检查，解决打包失败
        "skipLibCheck": true,
        // 解析非相对模块名的基准目录
        "baseUrl": "./",
        // 模块名到基于 baseUrl 的路径映射的列表。
        "paths": {
            "@": ["src"],
            "@/*": ["src/*"]
        }
    },
    "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
    "references": [{ "path": "./tsconfig.node.json" }],
    "exclude": ["node_modules", "dist", "**/*.js"]
}
