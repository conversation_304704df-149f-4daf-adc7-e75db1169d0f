<template>
  <div class="m-info">

  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TabsPaneContext } from 'element-plus'

const activeName = ref('first')
const toGitHub = (link) => {
  window.open(link)
}
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
</script>

<style lang="scss" scoped>
.m-info {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;

  .item-info-pop {
    display: flex;
    align-items: center;
  }

  .bell {
    color: black;
  }

  .item-child {
    display: flex;
    align-items: center;
    font-size: 13px;
  }
}

::v-deep(.el-divider--horizontal) {
  margin-bottom: 10px;
  margin-top: 10px;
}

.transverseMenu {
  .bell {
    color: white;
  }
}</style>
