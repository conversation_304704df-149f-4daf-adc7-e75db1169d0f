<template>
  <div class="page-container">
    <span class="page-text" v-show="!isTotal"
      >第1-{{ page.page_size }}条/总共{{ page.total }}条</span
    >
    <span class="total-text" v-show="isTotal">总共3页</span>
    <el-pagination
      v-model:page="page.page"
      :page-size="page.page_size"
      background
      layout="prev, pager, next"
      :total="page.total"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts">
export default {
  props: {
    page: {
      type: Object,
      default: {
        total: 0,
        page: 1,
        page_size: 10,
      },
    },
    isTotal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  methods: {
    handleCurrentChange(page) {
      this.$emit('handle-page', page)
    },
  },
}
</script>
<style lang="scss" scoped>
@import './index';
</style>