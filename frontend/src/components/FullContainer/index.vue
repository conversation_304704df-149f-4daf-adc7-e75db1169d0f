<template>
    <div class="m-full-screen-container">
        <div class="header">
            <div @click="goHome">
                <img src="@/assets/image/logo.png" class="logo-img" alt="" />
            </div>
            <div :class="{ 'title-container': isSHow }">
                <span class="title">{{ title }}</span>
            </div>
        </div>
        <div class="body-container">
            <slot></slot>
            <div class="back-container" v-show="bottmBarShow">
                <span @click="goBack">
                    <img class="c-back" src="@/assets/image/back.png" />
                </span>
            </div>
        </div>

        <div ref="domRef"></div>
    </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from "vue"

const mounted = ref<boolean>(true)

const domRef = ref<HTMLElement>()
let props = defineProps({
    bottmBarShow: {
        type: Boolean,
        default: true,
    },
    isSHow: {
        type: Boolean,
        default: true,
    },
    title: {
        type: String,
        default: "",
    },
})

const autoResizeScreen = () => {
    // const { width, height } = window.screen
    const { clientWidth, clientHeight } = document.body
    var width = 1920,
        height = 1080
    let left
    let top
    let scale
    // 获取比例  可视化区域的宽高比与 屏幕的宽高比  来进行对应屏幕的缩放
    if (clientWidth / clientHeight > width / height) {
        scale = clientHeight / height
        top = 0
        left = (clientWidth - width * scale) / 2
    } else {
        scale = clientWidth / width
        left = 0
        top = (clientHeight - height * scale) / 2
    }
    // 防止组件销毁后还执行设置状态s
    if (mounted.value) {
        Object.assign(domRef.value.style, {
            transform: `scale(${scale})`,
            left: `${left}px`,
            top: `${top}px`,
        })
    }
}

function goHome() {
    window.location.href = "/"
}

function goBack() {
    window.history.go(-1)
}
onMounted(() => {
    mounted.value = true
    autoResizeScreen()
    window.addEventListener("resize", autoResizeScreen)
})
onBeforeUnmount(() => {
    mounted.value = false
    window.removeEventListener("resize", autoResizeScreen)
})
</script>

<style lang="scss" scoped>
.m-full-screen-container {
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 auto;
    display: flex;
    background: url("@/assets/image/index.jpg") #4692c0 no-repeat center;
    background-size: 100%;
    position: relative;
    flex-direction: column;

    .c-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
    }

    .header {
        width: 100vw;
        height: 96px;
        background: linear-gradient(333deg, #004672 0%, #2c75b8 50%, #8ecde1 100%);
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;

        .logo-img {
            width: 200px;
            height: 96px;
            position: absolute;
            top: 0px;
            left: 40px;
        }

        .title-container {
            text-align: center;
            width: 644px;
            height: 72px;
            background: linear-gradient(180deg, #004672 0%, #2c75b8 50%, #3c87cd 100%);
            box-shadow: 0px 4px 30px 0px rgba(0, 39, 68, 0.3);
            border-radius: 0px 0px 80px 80px;
        }

        .title {
            width: 331px;
            font-size: 30px;
            // font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 500;
            color: #ffffff;
            line-height: 72px;
        }
    }

    .body-container {
        width: 100vw;
        height: 100vh;
        display: flex;
        margin-top: -96px;
        justify-content: center;

        .back-container {
            position: absolute;
            z-index: 99;
            bottom: 20px;
            display: flex;
            right: 40px;
            justify-content: end;

            .c-back {
                width: 72px;
                height: 72px;
                cursor: pointer;
            }
        }
    }
}
</style>
