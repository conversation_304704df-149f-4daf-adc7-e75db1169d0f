<template>
  <el-dialog :title="title" v-model="dialogVisible" append-to-body destroy-on-close class="scrollbar" width="500">

    <div>
      <el-row>
        <el-col :span="4">
          <div style="height: 30px;line-height:30px;">
            任务名称：
          </div>
        </el-col>
        <el-col :span="8">
          <el-input v-model="taskName" style="width: 240px" placeholder="定时任务名称"></el-input>
        </el-col>
      </el-row>
      <el-row style="margin-top: 20px;">
        <el-col :span="4">
          <div style="height: 30px;line-height:30px;">
            定时时间：
          </div>
        </el-col>
        <el-col :span="8">
          <el-time-picker v-model="taskTime" placeholder="请选择时间" :disabled-seconds="disabledSeconds"
            value-format="HH:mm:ss" />
        </el-col>
      </el-row>
      <el-row style="margin-top: 20px;">
        <el-col :span="5">
          <el-button type="primary" @click="submit">确定</el-button>
        </el-col>
        <el-col :span="5">
          <el-button @click="cancel">取消</el-button>
        </el-col>
      </el-row>
    </div>
  </el-dialog>

</template>

<script setup>
import { ref, defineProps, defineExpose, defineEmits } from 'vue'
import { AddCron, EditCron } from "@/api/cron"
const taskName = ref("")
const taskTime = ref("")
const taskId = ref(0)
const EditStatus = ref(false)
const title = ref("")
const emit = defineEmits(["refresh"])
const modelType = ref([
  { value: "everyDay", label: "每天" },
  { value: "everyweek", label: "每周" },
])
let props = defineProps({
  expression: { type: String, default: "12:12" },
  cronName: { type: String, default: "" }
})
const dialogVisible = ref(false)

const disabledSeconds = (hour, minute) => {
  return makeRange(1, 59)
}
const makeRange = (start, end) => {
  const result = []
  for (let i = start; i <= end; i++) {
    result.push(i)
  }
  return result
}
const showEdit = (type, id, name, time) => {
  if (type == 'edit') {
    title.value = "编辑定时复核时间"
    const [minutes, hours, dayOfMonth, month, dayOfWeek] = time.split(' ');
    EditStatus.value = true
    taskName.value = name
    taskId.value = id
    taskTime.value = hours + ":" + minutes + ":00"


    dialogVisible.value = true
  } else {
    title.value = "新增定时复核时间"
    EditStatus.value = false
    taskName.value = ""
    taskTime.value = ""
    taskId.value = 0
    dialogVisible.value = true
  }
}

defineExpose({
  showEdit,
})


function submit () {
  if (!taskName.value) {
    ElMessage.error("请输入任务名称")
    return
  }
  if (!taskTime.value) {
    ElMessage.error("请选择任务时间")
    return
  }
  let time = taskTime.value.split(":")
  let cronTime = time[1] + " " + time[0] + " * * *"
  let postData = {
    "cronName": taskName.value,
    "cronTime": cronTime,
    "cronType": 1,
  }
  if (EditStatus.value) {
    postData.id = taskId.value
    EditCron(postData).then(res => {
      let { code, message, data } = res.data
      if (code == 0) {
        ElMessage.success(message)
        emit("refresh")
        taskName.value = ""
        taskTime.value = ""
        dialogVisible.value = false
      } else {
        ElMessage.error(message)
      }

    })
  } else {
    AddCron(postData).then(res => {
      let { code, message, data } = res.data
      if (code == 0) {
        ElMessage.success(message)
        emit("refresh")
        taskName.value = ""
        taskTime.value = ""
        dialogVisible.value = false
      } else {
        ElMessage.error(message)
      }

    })
  }

}
function cancel () {
  taskName.value = ""
  taskTime.value = ""
  dialogVisible.value = false
}
</script>
<style scoped>
.pop_btn {
  text-align: center;
  margin-top: 20px;
}

.popup-main {
  position: relative;
  margin: 10px auto;
  background: #fff;
  border-radius: 5px;
  font-size: 12px;
  overflow: hidden;
}

.popup-title {
  overflow: hidden;
  line-height: 34px;
  padding-top: 6px;
  background: #f2f2f2;
}

.popup-result {
  box-sizing: border-box;
  line-height: 24px;
  margin: 25px auto;
  padding: 15px 10px 10px;
  border: 1px solid #ccc;
  position: relative;
}

.popup-result .title {
  position: absolute;
  top: -28px;
  left: 50%;
  width: 140px;
  font-size: 14px;
  margin-left: -70px;
  text-align: center;
  line-height: 30px;
  background: #fff;
}

.popup-result table {
  text-align: center;
  width: 100%;
  margin: 0 auto;
}

.popup-result table span {
  display: block;
  width: 100%;
  font-family: arial;
  line-height: 30px;
  height: 30px;
  white-space: nowrap;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.popup-result-scroll {
  font-size: 12px;
  line-height: 24px;
  height: 10em;
  overflow-y: auto;
}
</style>
