export const dateFormat = function (fmt) {


  var unixTimestamp = new Date(fmt * 1000);
  var commonTime = unixTimestamp


  return `${commonTime.getFullYear()}年${commonTime.getMonth() + 1 <= 9 ? commonTime.getMonth() + 1 : commonTime.getMonth() + 1}月${commonTime.getDate() <= 9 ? '0' + commonTime.getDate() : commonTime.getDate()}日`
};

export const timeFormat = function (fmt) {


  var unixTimestamp = new Date(fmt * 1000);
  var commonTime = unixTimestamp


  return `${commonTime.getFullYear()}/${commonTime.getMonth() + 1 <= 9 ? commonTime.getMonth() + 1 : commonTime.getMonth() + 1}/${commonTime.getDate() <= 9 ? '0' + commonTime.getDate() : commonTime.getDate()}  ${commonTime.getHours() <= 9 ? '0' + commonTime.getHours() : commonTime.getHours()}:${commonTime.getMinutes() <= 9 ? '0' + commonTime.getMinutes() : commonTime.getMinutes()}:${commonTime.getSeconds()}`
};