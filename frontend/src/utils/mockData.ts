// 模拟数据生成器
export class MockDataGenerator {
  // 疫苗类型
  static vaccineTypes = [
    '新冠疫苗(mRNA)',
    '新冠疫苗(灭活)',
    '流感疫苗',
    '乙肝疫苗',
    '狂犬疫苗',
    '肺炎疫苗',
    'HPV疫苗',
    '水痘疫苗'
  ]

  // 生产厂商
  static manufacturers = [
    '北京科兴',
    '国药集团',
    '康希诺',
    '智飞生物',
    '华兰生物',
    '长春长生',
    '成都生物'
  ]

  // 生成随机日期
  static randomDate(start: Date, end: Date): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  }

  // 生成批号
  static generateBatchNumber(): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    let batch = ''

    // 2个字母 + 4个数字
    for (let i = 0; i < 2; i++) {
      batch += letters.charAt(Math.floor(Math.random() * letters.length))
    }
    for (let i = 0; i < 4; i++) {
      batch += numbers.charAt(Math.floor(Math.random() * numbers.length))
    }

    return batch
  }

  // 生成系统状态数据
  static generateSystemStats() {
    return {
      totalVaccines: Math.floor(Math.random() * 500) + 200,
      activeLanes: Math.floor(Math.random() * 20) + 15,
      activeStations: Math.floor(Math.random() * 8) + 5,
      lowStockLanes: Math.floor(Math.random() * 5) + 1
    }
  }

  // 生成当日统计数据 - 更真实的疫苗推送数据
  static generateDailyStats() {
    const data = []
    const startHour = 8
    const endHour = 18

    // 真实的疫苗接种时间分布模式
    const hourlyPatterns = {
      8: { base: 25, variance: 8 },   // 开始工作
      9: { base: 45, variance: 12 },  // 上午高峰开始
      10: { base: 65, variance: 15 }, // 上午高峰
      11: { base: 58, variance: 13 }, // 上午高峰结束
      12: { base: 20, variance: 8 },  // 午休开始
      13: { base: 15, variance: 5 },  // 午休时间
      14: { base: 35, variance: 10 }, // 下午开始
      15: { base: 52, variance: 14 }, // 下午高峰
      16: { base: 48, variance: 12 }, // 下午高峰
      17: { base: 32, variance: 10 }, // 下午结束
      18: { base: 18, variance: 6 }   // 收尾工作
    }

    for (let hour = startHour; hour <= endHour; hour++) {
      const pattern = hourlyPatterns[hour]
      const count = Math.max(0, Math.floor(Math.random() * pattern.variance * 2) + pattern.base - pattern.variance)

      data.push({
        hour: `${hour.toString().padStart(2, '0')}:00`,
        count: count,
        vaccineTypes: {
          '新冠疫苗': Math.floor(count * 0.4),
          '流感疫苗': Math.floor(count * 0.25),
          '乙肝疫苗': Math.floor(count * 0.15),
          '其他': count - Math.floor(count * 0.8)
        }
      })
    }

    return data
  }

  // 生成疫苗类型统计 - 基于真实使用比例
  static generateVaccineTypeStats() {
    const typeDistribution = [
      { name: '新冠疫苗(mRNA)', value: 285, percentage: 35.6 },
      { name: '新冠疫苗(灭活)', value: 198, percentage: 24.8 },
      { name: '流感疫苗', value: 156, percentage: 19.5 },
      { name: '乙肝疫苗', value: 89, percentage: 11.1 },
      { name: 'HPV疫苗', value: 45, percentage: 5.6 },
      { name: '肺炎疫苗', value: 27, percentage: 3.4 }
    ]

    // 添加一些随机变化，但保持相对比例
    return typeDistribution.map(item => ({
      name: item.name,
      value: item.value + Math.floor(Math.random() * 40) - 20, // ±20的随机变化
      percentage: item.percentage
    }))
  }

  // 生成货道数据 - 13条货道的货架
  static generateLaneData() {
    const data = []
    const laneLetters = ['A', 'B', 'C', 'D']
    const statuses = ['online', 'offline', 'maintenance', 'error']

    laneLetters.forEach(letter => {
      for (let i = 1; i <= 13; i++) {
        let stock = Math.floor(Math.random() * 80) + 5 // 5-84支
        const vaccineType = this.vaccineTypes[Math.floor(Math.random() * this.vaccineTypes.length)]
        const manufacturer = this.manufacturers[Math.floor(Math.random() * this.manufacturers.length)]

        // 根据库存量决定状态
        let status = 'online'
        if (stock === 0) status = 'offline'
        else if (stock < 15) status = Math.random() > 0.8 ? 'maintenance' : 'online'
        else if (Math.random() > 0.92) status = statuses[Math.floor(Math.random() * statuses.length)]

        // 模拟一些特殊情况
        if (i === 7 && letter === 'B') status = 'maintenance' // B7维护中
        if (i === 13 && letter === 'D') status = 'offline' // D13离线
        if (i === 3 && letter === 'A') stock = 8 // A3库存不足

        data.push({
          id: `${letter}${i}`,
          name: `${letter}${i}`,
          vaccineName: vaccineType,
          manufacturer: manufacturer,
          stock: stock,
          status: status,
          batchNumber: this.generateBatchNumber(),
          expiryDate: this.randomDate(
            new Date(),
            new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          ).toISOString(),
          temperature: (Math.random() * 4 + 2).toFixed(1), // 2-6度
          lastUpdate: new Date().toISOString(),
          rackPosition: letter, // 货架位置
          laneNumber: i // 货道编号
        })
      }
    })

    return data
  }

  // 生成接种台数据 - 更真实的工作负载分布
  static generateStationData() {
    const data = []

    // 接种台的真实工作模式
    const stationProfiles = [
      { id: 1, efficiency: 0.95, baseCount: 68, name: '1号台(主力)' },
      { id: 2, efficiency: 0.92, baseCount: 65, name: '2号台(主力)' },
      { id: 3, efficiency: 0.88, baseCount: 62, name: '3号台(主力)' },
      { id: 4, efficiency: 0.85, baseCount: 58, name: '4号台' },
      { id: 5, efficiency: 0.82, baseCount: 54, name: '5号台' },
      { id: 6, efficiency: 0.78, baseCount: 48, name: '6号台' },
      { id: 7, efficiency: 0.70, baseCount: 35, name: '7号台(备用)' },
      { id: 8, efficiency: 0.65, baseCount: 28, name: '8号台(备用)' }
    ]

    stationProfiles.forEach(profile => {
      const variance = Math.floor(Math.random() * 20) - 10 // ±10的变化
      const todayCount = Math.max(0, profile.baseCount + variance)

      data.push({
        station: profile.name,
        count: todayCount,
        status: profile.efficiency > 0.8 ? 'active' : (Math.random() > 0.3 ? 'active' : 'standby'),
        efficiency: profile.efficiency,
        lastVaccine: this.vaccineTypes[Math.floor(Math.random() * this.vaccineTypes.length)],
        operator: `医护${profile.id}号`,
        startTime: profile.id <= 6 ? '08:00' : '09:00', // 备用台位晚开始
        totalToday: todayCount,
        avgTime: Math.floor(Math.random() * 3) + 3, // 3-5分钟每人
        waitingCount: profile.id <= 3 ? Math.floor(Math.random() * 8) + 2 : Math.floor(Math.random() * 3)
      })
    })

    return data
  }

  // 生成实时趋势数据
  static generateTrendData(hours: number = 24) {
    const data = []
    const now = new Date()

    for (let i = hours; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000)
      data.push({
        time: time.toISOString(),
        vaccineCount: Math.floor(Math.random() * 50) + 10,
        stationActive: Math.floor(Math.random() * 8) + 3,
        laneActive: Math.floor(Math.random() * 20) + 15
      })
    }

    return data
  }

  // 生成告警数据
  static generateAlerts() {
    const alertTypes = [
      { type: 'low_stock', message: '货道库存不足', level: 'warning' },
      { type: 'temperature', message: '温度异常', level: 'error' },
      { type: 'offline', message: '设备离线', level: 'error' },
      { type: 'maintenance', message: '设备需要维护', level: 'info' },
      { type: 'expired', message: '疫苗即将过期', level: 'warning' }
    ]

    const alerts = []
    const alertCount = Math.floor(Math.random() * 5) + 1

    for (let i = 0; i < alertCount; i++) {
      const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)]
      alerts.push({
        id: `alert_${i}`,
        ...alert,
        timestamp: this.randomDate(
          new Date(Date.now() - 24 * 60 * 60 * 1000),
          new Date()
        ).toISOString(),
        resolved: Math.random() > 0.7
      })
    }

    return alerts
  }

  // 生成完整的仪表盘数据
  static generateDashboardData() {
    return {
      systemStats: this.generateSystemStats(),
      dailyStats: this.generateDailyStats(),
      vaccineTypeStats: this.generateVaccineTypeStats(),
      laneData: this.generateLaneData(),
      stationData: this.generateStationData(),
      trendData: this.generateTrendData(),
      alerts: this.generateAlerts()
    }
  }
}
