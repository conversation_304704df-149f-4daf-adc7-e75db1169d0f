<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center>
    <el-form ref="form" :model="laneViewForm" :rules="rules" label-width="120px" label-position="right" inline>
      <el-form-item label="关联货架架编号">
        <el-select v-model="laneViewForm.lane_num" placeholder="请选择" clearable class="input_258">
          <el-option v-for="(item, index) in laneNum" :label="item.text" :value="item.value" :key="index" />
        </el-select>

      </el-form-item>
      <el-form-item label="排序方向">
        <el-select v-model="laneViewForm.direction" placeholder="请选择" clearable class="input_258">
          <el-option v-for="(item, index) in laneDirection" :label="item.text" :value="item.value" :key="index" />
        </el-select>

      </el-form-item>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(this.form)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import { getLaneInfo, editLaneInfo } from '@/api/laneView'
import { ref, defineEmits, onMounted } from 'vue';
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('关联长条屏配置')
const rules = ref({})
const emit = defineEmits(["refresh"])
const laneDirection = [
  {
    value: 1, text: "从左到右"
  }, {
    value: 2, text: "从右到左"
  }]

const laneNum = [
  { value: 1, text: "A" },
  { value: 2, text: "B" },
  { value: 3, text: "C" },
  { value: 4, text: "D" },
  { value: 5, text: "E" },
  { value: 6, text: "F" },
  { value: 7, text: "G" },
  { value: 8, text: "H" },
  { value: 9, text: "I" },
  { value: 10, text: "J" },
  { value: 11, text: "K" }

]


let laneViewForm = ref({
  "lane_num": 0,
  "direction": 0
})

function close() {
  laneViewForm = ref({})
}




const showEdit = (type, id) => {
  console.log(id, "iiiid");

  if (type == 'edit') {
    title.value = '关联长条屏配置'
    dataInputType = "edit"
    dataInputId = id
    LaneInfo(id)
  } else {
    dataInputType = ""
    dialogVisible.value = true
  }
}
let dataInputType = ""
let dataInputId = ""
const LaneInfo = (id) => {
  getLaneInfo(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      laneViewForm = ref({ ...data.data })
      dialogVisible.value = true
    }
  })
}





const handleConfirm = async (done: () => void) => {

  await form.value.validate((valid, fields) => {
    if (valid) {

      if (dataInputType == 'edit') {
        if (dataInputId == "") {
          return false
        }

        editLaneInfo(dataInputId, laneViewForm.value).then(res => {
          let data = res.data
          if (data.code == 0) {
            dialogVisible.value = false
            laneViewForm = ref({})
            emit("refresh")
          } else {
            Alert("修改失败", "请检查录入信息是否有误")
          }
        })


      }


    } else {
      console.log('error submit!', fields)
    }
  })
}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}


onMounted(() => {


})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
