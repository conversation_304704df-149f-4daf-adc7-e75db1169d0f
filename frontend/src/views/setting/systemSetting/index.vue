<template>
  <full-container :isSHow="false" :title="Title">

    <div class="container-bg">
      <div class="table-box">
        <div class="systemLogo">
          <div class="system_title">管理后台logo设置</div>
          <div>上传图片 <el-upload class="avatar-uploader"
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :show-file-list="false"
              :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload></div>
          <div>
            <el-button type="primary" @click="changeSyatem">修改</el-button>
          </div>
        </div>

        <div class="systemTitle">
          <div class="system_title">管理后台名称设置</div>
          <div class="system-input-warp">
            <el-input v-model="systemName" style="width:260px"></el-input>
          </div>
          <div class="system-button-warp">
            <el-button type="primary" @click="changeSyatem">修改</el-button>
          </div>
        </div>
      </div>

    </div>

  </full-container>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, UploadProps } from 'element-plus'

import { ChangeSystemName, ReplaceLogo } from "../../../../wailsjs/go/utils/utils";



const Title = ref<String>('系统设置')
let imageUrl = ref("");
let systemName = ref(localStorage.getItem("SYSTEMNAME") || "全自动疫苗智能传输系统")
const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  imageUrl.value = URL.createObjectURL(uploadFile.raw!)
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png' && rawFile.type !== 'image/gif') {
    ElMessage.error('格式不符合')
    return false
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('Logo大小超过2MB')
    return false
  }
  console.log(rawFile, "rawFilerawFile");

  imageUrl.value = URL.createObjectURL(rawFile)
  return true
}

function changeSyatem() {
  localStorage.setItem("SYSTEMNAME", systemName.value)
}

function replaceLogo() {

}


onMounted(() => {

})
</script>

<style lang="scss" scoped>
.system-input-warp {
  margin: 20px 0
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.systemLogo,
.systemTitle {
  padding: 20px
}

.system_title {
  font-size: 20px;
}


@import './index';
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>