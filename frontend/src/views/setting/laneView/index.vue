<template>
  <full-container :isSHow="false" :title="Title">

    <div class="container-bg">
      <div class="table-box">
        <el-table :data="LaneListData" border style="width: 100%" class="table">
          <el-table-column label="长条屏设备ID" min-width="150" prop="device_id" />
          <el-table-column label="长条屏设备IP地址" min-width="120" prop="ip_address" />
          <el-table-column label="关联货道架编号" min-width="300" prop="lane_num" :formatter="showLaneNum" />
          <el-table-column label="排序方向" min-width="150" prop="direction" :formatter="showDirection" />
          <el-table-column label="操作" fixed="right" width="300">
            <template #default="scope">
              <el-button type="primary" @click="editLaneInfo(scope.row.device_id)">编辑</el-button>
              <el-button type="danger" @click="deleteLaneInfo(scope.row.device_id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
            layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
        </div>
      </div>

    </div>

    <Edit ref="edit" @refresh="LaneList" />
  </full-container>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import Edit from './components/laneViewEdit.vue'
import { getLaneList, deleteLane } from '@/api/laneView'
const edit = ref()
const Title = ref<String>('长条屏设置')
let LaneListData = ref([]);

let pageData = reactive({
  total: 0,
  page: 1,
  pageSize: 12,
})

function editLaneInfo(id) {
  edit.value.showEdit('edit', id)

}

function deleteLaneInfo(id) {
  ElMessageBox.confirm(
    '是否确定删除该长条屏设备信息?',
    '操作提示',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
    }
  )
    .then(() => {
      deleteLane(id).then((res) => {
        let { data, code, massage } = res.data
        if (code == 0) {
          ElMessage({
            type: 'success',
            message: '删除成功！',
          })
          LaneList()
        } else {
          ElMessage({
            type: 'error',
            message: '删除失败！',
          })
        }

      })

    })
}

function handlePage(page) {
  console.log(page)
  pageData.page = page
}

function LaneList() {
  getLaneList(pageData).then((res) => {
    let data = res.data
    if (data.code == 0) {

      LaneListData.value = data.data.data
      pageData.total = data.data.total
      pageData.page = data.data.current_page
      pageData.pageSize = data.data.per_page
    }

  })
}

function showLaneNum(row) {
  if (row.lane_num == 0) {
    return "未关联货道架"
  }
  const numtoList = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"]
  return numtoList[row.lane_num - 1]
}

function showDirection(row) {
  if (row.direction == 0) {
    return "未设置排序方向"
  }
  return row.direction == 1 ? "从左到右" : "从右到左"


}




onMounted(() => {
  LaneList()
})
</script>

<style lang="scss" scoped>
@import './index';

.edit-link {
  color: #fff
}
</style>