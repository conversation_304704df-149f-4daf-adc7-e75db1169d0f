.container-bg {
    width: calc(100vw - 80px);
    height: calc(100vh - 240px);
    background: #ffffff;
    border-radius: 8px;
    margin-top: 136px;
    position: relative;
    display: flex;
    overflow: hidden;
    .table-box {
        flex: 10;
        width: 35%;
        flex-direction: row;
    }
    .table-nil-box {
        flex: 1;
    }
    .pagination {
        border-top: 4px solid #fafafa;
        padding-top: 10px;

        display: flex;
        justify-content: end;
        bottom: 10px;
        right: 0px;
        width: 100%;
        box-sizing: border-box;
        margin-right: 20px;
    }
    .el-pagination {
        margin-right: 24px;
    }
    .table {
        height: 700px;
    }
}
.tab-bar-container {
    :deep(.tar-bar) {
        cursor: pointer;
        position: absolute;
        z-index: 99;
        right: -8px;
        width: 285px;
        height: 136px;
        background: url("@/assets/image/tarbar.png") no-repeat center center;
        display: flex;
        align-items: center;
        align-self: center;
        justify-content: center;
        .name {
            margin-left: 40px;
            width: 136px;
            height: 47px;
            font-size: 22px;
            // font-family: YouSheBiaoTiHei;
            color: #f69813;
            line-height: 47px;
            font-weight: bold;
        }
    }
}
