<template>
  <full-container :isSHow="false" :title="Title">
    <div class="container-bg">
      <div class="setting-list">
        <div v-for="(item, index) in settingList" :key="index" class="vaccine-item" @click="hanleClick(item.uri)">
          <img :src="getSrc(item.image_url)" alt="" class="icon-img" />
          <span class="name">{{ item.name }}

          </span>
        </div>
      </div>
    </div>
  </full-container>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getSrc } from "@/utils/image"
const router = useRouter()

const Title = ref<String>('设置')
const settingList = [
  {
    image_url: 'stock.png',
    name: '疫苗相关设置',
    uri: "/vaccineStock/index"
  },
  {
    image_url: 'sort.png',
    name: 'AVM设置',
    uri: "/sortSetting/index"

  },
  {
    image_url: 'realtime.png',
    name: '长条屏设置',
    uri: "/setting/laneView"
  },
  {
    image_url: 'realtime.png',
    name: '电子标签管理',
    uri: "/tagPages"
  },
  {
    image_url: 'sortset.png',
    name: '工控日志',
    uri: "/ipc/log"
  },
  {
    image_url: 'port.png',
    name: '工控相关',
    uri: "/ipc"
  },
  {
    image_url: 'port.png',
    name: '货道设置',
    uri: "/freightLane"
  },
  {
    image_url: 'sortset.png',
    name: '系统设置',
    uri: "/setting/systemSetting"

  },
]

const hanleClick = (uri) => {

  router.push(uri)

}


</script>

<style lang="scss" scoped>
@import './index';
</style>
