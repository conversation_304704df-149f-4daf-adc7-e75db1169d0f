<template>
  <div class="login-container">
    <div class="login-box">
      <SwitchDark class="login-dark" />
      <div class="login-left">
        <img src="@/assets/image/login/side-logo.png">
      </div>
      <div class="login-form">


        <LoginForm v-if="accountLogin" />
        <LoginQrcode v-else />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import LoginForm from './components/LoginForm.vue'
import SwitchDark from '@/components/SwitchDark/index.vue'
import LoginQrcode from './components/LoginQrcode.vue'


const accountLogin = ref<boolean>(true)

const handleClick = () => {

  accountLogin.value = !accountLogin.value
}
</script>
<style lang="scss" scoped>
.login-left {
  height: 100vh;

  background-size: cover;
}

@import "./index";
</style>
