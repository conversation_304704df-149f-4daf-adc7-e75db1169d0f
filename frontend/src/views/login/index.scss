$dark_gray: #889aa4;

.login-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  .login-left {
    width: 50%;

    img {
      width: 100%;
      max-width: 900px;
    }
  }

  .login-form {
    max-width: 480px;
    width: 50%;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    box-sizing: border-box;
    position: relative;

    .info-qrcode {
      width: 150px;
      height: 40px;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      position: absolute;
      top: -60px;
      right: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #1492ff;
      font-size: 16px;

      &::after {
        content: "";
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 10px;
        height: 26px;
        border-top: 13px solid #fff;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 13px solid transparent;
        position: absolute;
        top: 40px;
        right: 16px;
      }
    }
  }

  .login-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;

    .title {
      margin: 0;
      font-size: 30px;
      white-space: nowrap;
    }

    .icon {
      width: 60px;
    }
  }

  ::v-deep(.el-input__inner) {
    height: 40px;
  }
}

.login-btn {
  margin-top: 20px;
  width: 100%;
  height: 47px
}

.show-pwd {
  position: absolute;
  right: 10px;
  top: 7px;
  font-size: 16px;
  color: $dark_gray;
  cursor: pointer;
  user-select: none;

  ::v-deep(.svg-icon) {
    vertical-align: 0;
  }
}

.login-container {
  background-color: #f0f2f5;
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 25px 25px;
  box-sizing: border-box;
}

.login-dark {
  position: absolute;
  right: 20px;
  top: 20px;
}

.qrcode {
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  width: 30px;
}

.login-qrcode {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  img {
    width: 60%;
  }

  .title {}
}

@media (max-width: 850px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    .login-form {
      width: 88%;

      .title {
        font-size: 20px;
      }
    }
  }

  .login-left {
    display: none;
  }
}