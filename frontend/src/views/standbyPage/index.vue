<template>
  <full-container :isSHow="true" :title="Title">
    <div class="tab-bar-container">
      <div class="tar-bar">
        <span class="wendu-text"><img src="@/assets/image/wdj-icon.png" alt="" class="wendu-icon" />

          <text class="more-text">温度:</text>
          <text class="big-text">6°C</text>
        </span>
        <span class="wendu-text" style="margin-left: 30px"><img src="@/assets/image/yd-icon.png" alt=""
            class="wendu-icon" />
          <text class="more-text">湿度:</text>
          <text class="big-text">76%</text>
        </span>
      </div>
    </div>
    <div class="container-bg">
      <div class="container-item">
        <div class="item" v-for="(item, index) in itemList" :key="index">
          <div class="item-top-text">{{ item.text }}</div>
          <div class="item-content">
            <span :class="[`bg_color_${item.type}`]" :style="{ height: `${item.number}%` }">
              <span class="item-text">{{ item.text }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="page-status">
      <div style="display: flex; align-items: end; margin-top: -8px">
        <span class="status-text">设备状态：</span>
        <span class="status-start">启动中</span>
        <span class="status-wait">待机中</span>
      </div>
    </div>
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const Title = localStorage.getItem("SYSTEMNAME") || "全自动疫苗智能传输系统"
const pageData = {
  total: 30,
  page: 1,
  page_size: 10,
}
const isTotal = ref<Boolean>(true)
const itemList = [
  {
    text: 'b1',
    type: 1,
    number: 40,
  },
  {
    text: 'b1',
    type: 2,
    number: 30,
  },
  {
    text: 'b1',
    type: 3,
    number: 10,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  }, {
    text: 'b1',
    type: 1,
    number: 40,
  },
  {
    text: 'b1',
    type: 2,
    number: 20,
  },
  {
    text: 'b1',
    type: 3,
    number: 30,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  }, {
    text: 'b1',
    type: 1,
    number: 40,
  },
  {
    text: 'b1',
    type: 2,
    number: 30,
  },
  {
    text: 'b1',
    type: 3,
    number: 30,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  }, {
    text: 'b1',
    type: 1,
    number: 40,
  },
  {
    text: 'b1',
    type: 2,
    number: 30,
  },
  {
    text: 'b1',
    type: 3,
    number: 30,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  }, {
    text: 'b1',
    type: 1,
    number: 40,
  },
  {
    text: 'b1',
    type: 2,
    number: 30,
  },
  {
    text: 'b1',
    type: 3,
    number: 30,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  },
  {
    text: 'b1',
    type: 1,
    number: 100,
  },
]
const vaccineData = [
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
  { name: '123213' },
]
const formdata = reactive({
  name: '',
  date: '',
})
const vaccineName = []

function handlePage(page) {
  console.log(page)
  pageData.page = page
}
</script>

<style lang="scss" scoped>
@import './index';
</style>
