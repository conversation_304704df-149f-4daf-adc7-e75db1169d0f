<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center
    :close-on-click-modal="false">
    <el-form :model="vaccineForm" :rules="rules" label-width="120px" label-position="right" inline>
      <el-form-item label="疫苗名称" required>
        <el-autocomplete v-model.trim="vaccineForm.vaccine_name" :fetch-suggestions="vaccinumSearch" clearable
          value-key="vaccine_name" class="input_258" placeholder="选择或输入" @select="vaccineSelect" />
      </el-form-item>
      <el-form-item label="疫苗码">
        <el-input v-model.trim="vaccineForm.vaccine_code" disabled class="input_258" />
      </el-form-item>
      <el-form-item label="企业名称" required>
        <el-autocomplete v-model.trim="vaccineForm.enterprise_name" :fetch-suggestions="enterpriseSearch" clearable
          value-key="enterprise_name" class="input_258" placeholder="选择或输入" @select="enterpriseSelect" />
      </el-form-item>
      <el-form-item label="企业码">
        <el-input v-model.trim="vaccineForm.enterprise_code" disabled class="input_258" />
      </el-form-item>
      <el-form-item label="单盒规格" required>
        <el-input v-model.number="vaccineForm.spec_single" clearable placeholder="请输入" class="input_210" />
        <span class="w_48">支/盒</span>
      </el-form-item>

      <el-form-item label="接种人份" required>
        <el-input v-model.number="vaccineForm.jz_num" clearable placeholder="请输入" class="input_210" />
        <span class="w_48">份/支</span>
      </el-form-item>


      <el-form-item label="是否多人份" required>
        <el-select v-model="vaccineForm.is_multi" class="m-2" placeholder="Select" size="large" style="width: 240px">
          <el-option v-for="item in isMulti" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

      </el-form-item>
      <el-form-item label="疫苗规格">
        <el-input v-model.trim="vaccineForm.spec" clearable placeholder="请输入" class="input_258" />
      </el-form-item>


    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { addVaccinumInfo, getVaccinumDetail, editVaccinumInfo, getVaccinumNameList } from '@/api/vaccinum'
import { getVaccinumEnterpriseList } from '@/api/manufactor'
import { ref, reactive, defineEmits, onMounted } from 'vue';

const dialogVisible = ref<boolean>(false)
const title = ref('新增疫苗信息')
const rules = reactive({})
const emit = defineEmits(["refresh"])

const vaccinumName = ref([])
const vaccinumEnterprise = ref([])
const isMulti = [{ key: 0, label: '是', value: 1 }, { key: 1, label: '否', value: 2 }]

let vaccineForm = reactive({
  "vaccine_name": "",
  "vaccine_code": "",
  "enterprise_name": "",
  "enterprise_code": "",
  "spec_single": 1,
  "is_multi": 2,
  "spec": "",
  "jz_num": 1
})
const formText = { "vaccine_name": "疫苗名称", "enterprise_name": "企业名称", "spec_single": "单盒规格", "jz_num": "接种人份", "is_multi": "是否多人份" }
function close() {
  vaccineForm = reactive({
    "vaccine_name": "",
    "vaccine_code": "",
    "enterprise_name": "",
    "enterprise_code": "",
    "spec_single": 1,
    "is_multi": 2,
    "spec": "",
    "jz_num": 1
  })
}


const vaccineSelect = (item: any) => {
  vaccineForm.vaccine_code = item.vaccine_code
}
const enterpriseSelect = (item: any) => {
  vaccineForm.enterprise_code = item.enterprise_code
}


const showEdit = (type, id) => {
  if (type == 'edit') {
    title.value = '修改疫苗信息'
    dataInputType = "edit"
    dataInputId = id
    VaccinumDetail(id)
  } else {
    title.value = '新增疫苗信息'
    dataInputType = ""
    dialogVisible.value = true
  }
}
let dataInputType = ""
let dataInputId = 0
const VaccinumDetail = (id) => {
  getVaccinumDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      vaccineForm.enterprise_code = data.data.enterprise_code
      vaccineForm.enterprise_name = data.data.enterprise_name
      vaccineForm.vaccine_code = data.data.vaccine_code
      vaccineForm.vaccine_name = data.data.vaccine_name
      vaccineForm.spec = data.data.spec
      vaccineForm.spec_single = data.data.spec_single
      vaccineForm.is_multi = data.data.is_multi
      vaccineForm.jz_num = data.data.jz_num

      dialogVisible.value = true
    }
  })
}

interface LinkItem {
  value: string
  link: string
}
const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? vaccinumName.value.filter(selectFilter(queryString, "vaccine_name")) : vaccinumName.value
  cb(results)
}

const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? vaccinumEnterprise.value.filter(selectFilter(queryString, "enterprise_name")) : vaccinumEnterprise.value
  // call callback function to return suggestion objects
  cb(results)
}


const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}



const handleConfirm = async () => {
  for (let key in vaccineForm) {


    if (key !== "spec" && vaccineForm[key] == "" || key !== "spec" && vaccineForm[key] == 0) {
      ElMessage.error({
        message: `请填写或选择【${formText[key]}】`,
        type: 'error'

      })
      return
    }

  }

  if (dataInputType == 'edit') {
    if (dataInputId == 0) {
      return false
    }

    editVaccinumInfo(dataInputId, vaccineForm).then(res => {
      let data = res.data
      if (data.code == 0) {
        dialogVisible.value = false
        vaccineForm = reactive({})
        emit("refresh")
      } else {
        Alert("提交失败！", "请检查录入信息是否有误")
      }
    })


  } else {
    addVaccinumInfo(vaccineForm).then(res => {

      let data = res.data
      if (data.code == 0) {
        dialogVisible.value = false
        vaccineForm = reactive({})
        emit("refresh")
      } else {
        Alert("提交失败！", "请检查录入信息是否有误")
      }
    })
      .catch((err) => {
        console.log(err, "dfdfdsfsdfsdf");

        ElMessage.error({
          message: err.response.data.message,
          type: 'error',

        })
      })


  }




}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}
function VaccinumNameList() {
  getVaccinumNameList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      vaccinumName.value = data.data
    }

  })

}


function VaccinumEnterpriseList() {
  getVaccinumEnterpriseList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      vaccinumEnterprise.value = data.data
    }

  })

}
onMounted(() => {
  VaccinumNameList()
  VaccinumEnterpriseList()
})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
