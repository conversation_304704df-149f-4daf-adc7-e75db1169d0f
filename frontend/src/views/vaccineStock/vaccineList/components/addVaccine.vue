<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center
    style="width: 400px;height:350px" :close-on-click-modal="false">
    <el-upload ref="uploadRef" class="upload-demo" :action="Url" :auto-upload="false" :on-success="handleSuccess"
      :on-error="handleError">
      <template #trigger>
        <el-button type="primary" size="large">选择导入文件</el-button>
      </template>
      <el-row>
        <el-col>
          <el-button class="ml-3" type="success" size="large" @click="submitUpload">确定导入</el-button>
        </el-col>
      </el-row>

      <template #tip>
        <div class="el-upload__tip">
          Excel文件不能大于20MB
        </div>
      </template>
    </el-upload>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox, ElMessage } from 'element-plus'
import type { UploadProps, UploadUserFile, UploadInstance } from 'element-plus'
import { addVaccinumInfo, getVaccinumDetail, editVaccinumInfo, getVaccinumNameList } from '@/api/vaccinum'
import { getVaccinumEnterpriseList } from '@/api/manufactor'
import { ref, reactive, defineEmits, onMounted } from 'vue';
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('表格导入疫苗信息')
const rules = reactive({})
const emit = defineEmits(["refresh"])

const vaccinumName = ref([])
const vaccinumEnterprise = ref([])
const isMulti = [{ key: 0, label: '否', value: 1 }, { key: 1, label: '是', value: 2 }]
const Url = import.meta.env.VITE_API_BASE_URL + "manage/vaccin/into"
let vaccineForm = reactive({
  "vaccine_name": "",
  "vaccine_code": "",
  "enterprise_name": "",
  "enterprise_code": "",
  "spec_single": "",
  "is_multi": 1,
  "spec": "",
  "jz_num": ""
})


const uploadRef = ref<UploadInstance>()

const submitUpload = () => {
  uploadRef.value!.submit()
}

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  console.log(file, uploadFiles)
}

const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
  console.log(uploadFile)
}

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning(
    `The limit is 3, you selected ${files.length} files this time, add up to ${files.length + uploadFiles.length
    } totally`
  )
}

const handleSuccess = (response, uploadFile) => {

  if (response.code == 0) {
    ElMessage.success("导入成功")
    dialogVisible.value = false
    emit("refresh")
  } else {
    ElMessage.error(response.msg.message)
  }
}
const handleError = (err, uploadFile) => {
  let msg = JSON.parse(err.message)


  ElMessage({
    showClose: true,
    message: "导入失败!" + msg.message,
    type: 'error',
    duration: 5000

  })
}
const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
  return ElMessageBox.confirm(
    `Cancel the transfer of ${uploadFile.name} ?`
  ).then(
    () => true,
    () => false
  )
}

function close() {
  vaccineForm = reactive({})
}


const vaccineSelect = (item: any) => {
  vaccineForm.vaccine_code = item.vaccine_code
}
const enterpriseSelect = (item: any) => {
  vaccineForm.enterprise_code = item.enterprise_code
}


const showBox = () => {


  dialogVisible.value = true

}
let dataInputType = ""
let dataInputId = 0
const VaccinumDetail = (id) => {
  getVaccinumDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      vaccineForm = reactive({ ...data.data })
      dialogVisible.value = true
    }
  })
}

interface LinkItem {
  value: string
  link: string
}
const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? vaccinumName.value.filter(selectFilter(queryString, "vaccine_name")) : vaccinumName.value
  cb(results)
}

const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? vaccinumEnterprise.value.filter(selectFilter(queryString, "enterprise_name")) : vaccinumEnterprise.value
  // call callback function to return suggestion objects
  cb(results)
}


const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}


function VaccinumNameList() {
  getVaccinumNameList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      vaccinumName.value = data.data
    }

  })

}


function VaccinumEnterpriseList() {
  getVaccinumEnterpriseList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      vaccinumEnterprise.value = data.data
    }

  })

}
onMounted(() => {
  VaccinumNameList()
  VaccinumEnterpriseList()
})
defineExpose({
  showBox,
})
</script>

<style lang="scss" scoped>
@import '../index';

.upload-demo {
  text-align: center;
}
</style>
