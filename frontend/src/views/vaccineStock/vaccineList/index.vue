<template>
  <full-container :isSHow="false" :title="vaccineTitle">
    <div class="tab-bar-container">
      <div class="filtrate-box">
        <div class="filtrate">
          <div class="select-item">
            <b>疫苗名称：</b>
            <el-autocomplete v-model.trim="selectVaccineName" :fetch-suggestions="vaccinumSearch" clearable
              value-key="vaccine_name" class="input_258" placeholder="选择或输入" @select="vaccineSelect"
              @keyup.enter="VaccinumList" @change="vaccineSelect" />
          </div>
          <div class="select-item">
            <b>生产企业：</b>
            <el-autocomplete v-model.trim="selectManufacturer" :fetch-suggestions="enterpriseSearch" clearable
              value-key="enterprise_name" class="input_258" placeholder="选择或输入" @select="enterpriseSelect"
              @change="enterpriseSelect" @keyup.enter="VaccinumList" />
          </div>
        </div>
        <el-button type="default" class="filtrate-button" @click="filtrateReset">重置</el-button>
        <el-button type="primary" class="filtrate-button" @click="VaccinumList">查询</el-button>
      </div>
      <div class="tar-bar" @click="addVaccinde()">
        <text class="name">新增疫苗</text>
      </div>
      <div class="tar-bar-import" @click="addVaccinum()">
        <text class="name">导入疫苗表</text>
      </div>

    </div>

    <div class="container-bg">
      <el-table :data="vaccineData" border style="width: 100%" class="table">
        <el-table-column label="序号" min-width="65" type="index" />
        <el-table-column label="疫苗名称" min-width="300" prop="vaccine_name" />
        <el-table-column label="疫苗码" min-width="150" prop="vaccine_code" />
        <el-table-column label="企业名称" min-width="200" prop="enterprise_name" />
        <el-table-column label="企业码" min-width="100" prop="enterprise_code" />
        <el-table-column label="疫苗规格" min-width="200" prop="spec" />
        <el-table-column label="单盒规格（支/盒）" width="110" prop="spec_single" />
        <el-table-column label="是否多人份" width="100" prop="is_multi" :formatter="IsMulti" column-key="label" />
        <el-table-column label="接种人份（份/支）" width="100" prop="jz_num" />
        <el-table-column label="存放高度（mm）" min-width="80" prop="height" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button size="default" type="primary" @click="editVaccinde(scope.row.id)">编辑</el-button>
            <el-button size="default" type="danger" @click="delVaccinum(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">

        <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background="true"
          layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
      </div>
    </div>

    <Edit ref="edit" @refresh="VaccinumList" />
    <ADDvaccine ref="addVaccineBox" @refresh="VaccinumList" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox } from 'element-plus'
import Edit from './components/vaccineEdit.vue'
import ADDvaccine from './components/addVaccine.vue'
import { getVaccinumList, deleteVaccinum, getVaccinumfromManufacturer, getVaccinumfromVaccineName } from '@/api/vaccinum'
const edit = ref()
const addVaccineBox = ref()
var Manufacturer = ref([])
var VaccineName = ref([])
var selectBatchNum = ref("")
var selectVaccineName = ref("")
var selectManufacturer = ref("")
var selectVaccineCode = ref("")
var selectManufacturerCode = ref("")
const isMulti = [{ key: 0, label: '否', value: 1 }, { key: 1, label: '是', value: 2 }]
const vaccineTitle = ref<String>('疫苗信息列表')
let vaccineData = ref([]);
let pageData = reactive({
  total: 0,
  page: 1,
  pageSize: 10,
})




const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? VaccineName.value.filter(selectFilter(queryString, "vaccine_name")) : VaccineName.value
  cb(results)
}

const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? Manufacturer.value.filter(selectFilter(queryString, "enterprise_name")) : Manufacturer.value
  // call callback function to return suggestion objects
  cb(results)
}


const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}


const vaccineSelect = (item: any) => {

  selectVaccineCode.value = item.vaccine_code || ""
}
const enterpriseSelect = (item: any) => {
  selectManufacturerCode.value = item.enterprise_code || ""
}

function filtrateReset() {

  selectVaccineName.value = ""
  selectVaccineCode.value = ""
  selectManufacturerCode.value = ""
  selectManufacturer.value = ""
  pageData.page = 1
  VaccinumList()
}

function IsMulti(row, index) {
  return row.is_multi == 1 ? '是' : '否'

}
function handlePage(page) {
  pageData.page = page
  VaccinumList()
}
const addVaccinde = () => {
  edit.value.showEdit('add')
}

const addVaccinum = () => {
  addVaccineBox.value.showBox()
}
const editVaccinde = (id) => {
  edit.value.showEdit('edit', id)

}

const delVaccinum = (id) => {
  Confirm("删除疫苗信息", "是否确认删除该疫苗信息", () => {
    console.log(id);
    deleteVaccinum(id).then((res) => {
      let data = res.data
      if (data.code == 0) {
        VaccinumList()
      }
    })
  })
}


function VaccinumList() {
  let data = {
    enterprise_code: selectManufacturerCode.value,
    vaccine_code: selectVaccineCode.value,
    ...pageData
  }
  getVaccinumList(data).then((res) => {

    let data = res.data
    if (data.code == 0) {
      pageData.total = data.data.total
      pageData.page = data.data.current_page
      pageData.pageSize = data.data.per_page
      vaccineData.value = data.data.data
    }

  })

}

function VaccinumfromManufacturer() {
  getVaccinumfromManufacturer().then((res) => {
    let data = res.data
    if (data.code == 0) {
      Manufacturer.value = data.data
    }
  })
}

function VaccinumfromVaccineName() {
  getVaccinumfromVaccineName().then((res) => {
    let data = res.data
    if (data.code == 0) {
      VaccineName.value = data.data
    }
  })
}

const Confirm = (title: string, msg: string, fun: any) => {
  ElMessageBox.confirm(`${msg}`, `${title}`, {
    distinguishCancelAndClose: true,
    confirmButtonText: '确定删除',
    cancelButtonText: '取消'
  })
    .then(() => {
      console.log("删除成功");
      fun()
    })
    .catch(() => {
      console.log("取消删除");

    })
}


onMounted(() => {
  VaccinumList();
  VaccinumfromManufacturer()
  VaccinumfromVaccineName()
})
</script>

<style lang="scss" scoped>
@import './index';
</style>