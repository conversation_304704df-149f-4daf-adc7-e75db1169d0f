<template>
    <full-container :isSHow="false" :title="vaccineTitle">
        <div class="container-bg">
            <div v-for="(item, index) in vaccineList" :key="index" class="vaccine-item" @click="hanleClick(index)">
                <img :src="getSrc(item.image_url)" alt="" class="icon-img" />
                <span class="name">{{ item.name }}</span>
            </div>
        </div>
    </full-container>
</template>
<script lang="ts" setup>
import { breakpointsTailwind } from '@vueuse/core';
import { ref } from 'vue'
import { getSrc } from "@/utils/image"
import { useRouter } from 'vue-router'
const router = useRouter()
const vaccineTitle = ref<String>('疫苗信息相关设置')
const vaccineList = [
    { image_url: 'set.png', name: '疫苗信息管理' },
    { image_url: 'set.png', name: '疫苗批号管理' },
    { image_url: 'set.png', name: '分拣货道管理' },
    { image_url: 'set.png', name: '疫苗名称管理' },
    { image_url: 'set.png', name: '疫苗厂商管理' },
    // { image_url: '@/assets/image/search.png', name: '疫苗库存查询' },
]

const hanleClick = (index) => {

    switch (index) {
        case 0:
            router.push('/vaccineStock/vaccineList')
            break;
        case 1:
            router.push('/vaccineStock/vaccineBatch')
            break;
        case 2:
            router.push('/vaccineStock/sortingParam')
            break;
        case 3:
            router.push('/vaccineStock/vaccineInfo')
            break;
        case 4:
            router.push('/vaccineStock/vaccineManufactor')
            break;
        case 5:
            router.push('/vaccineStock/vaccineSku')
            break;
    }

}
</script>

<style lang="scss" scoped>
@import './index';
</style>
