<template>
  <full-container :isSHow="false" :title="title">
    <div class="tab-bar-container">
      <div class="tar-bar" @click="oneKey">
        <text class="name">一键新增货道</text>
      </div>
    </div>
    <div class="container-bg">
      <el-table :data="LaneData" border style="width: 100%" class="table"
        :header-cell-style="{ height: '50px', 'line-height': '50px', 'font-size': '14px' }">
        <el-table-column label="货道架" width="70" prop="lane_num" />
        <el-table-column label="货道号" width="70" prop="line_num" />
        <el-table-column label="货道名称" width="86" prop="line_name" />
        <!-- <el-table-column label="RFID标签" width="100" prop="rfid" /> -->
        <el-table-column label="疫苗名称" min-width="200" prop="vaccine_name" />
        <el-table-column label="企业名称" min-width="100" prop="enterprise_name" />
        <el-table-column label="规格" min-width="100" prop="spec" />
        <el-table-column label="批号" min-width="150" prop="batch_num" />
        <el-table-column label="有效期" width="170" prop="valid" :formatter="Valid" />
        <el-table-column label="数量" width="70" prop="stock" />
        <el-table-column label="货道高度" width="90" prop="height" />
        <el-table-column label="配重高度" width="90" prop="balance_height" />

        <el-table-column fixed="right" label="操作" width="240">
          <template #default="scope">
            <el-button size="default" type="success" @click="editBalanceHeight(scope.row.id)">设置货道信息</el-button>
            <el-button size="default" type="warning" @click="editFreightLane(scope.row.id)">换苗</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page">
        <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
          layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
      </div>
    </div>
    <Edit ref="edit" @refresh="getLanePainList" />
    <BalanceHeight ref="balanceHeight" @refresh="getLanePainList" />
    <OneKey ref="onekey" @refresh="getLanePainList" />
  </full-container>
</template>


<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { dateFormat } from '@/utils/date'
import Edit from './components/paramEdit.vue'
import OneKey from './components/oneKey.vue'
import BalanceHeight from './components/balanceHeight.vue'
import { LanePainList, delFreightLane } from '@/api/lane'
import TableComponent from './components/tableComponent.vue'

const router = useRouter()
const edit = ref()
const onekey = ref()
const balanceHeight = ref()
const title = ref('分拣货道参数设置')
const LaneData = ref([]) // 假设从外部传入

const pageData = ref({ total: 30, page: 1, pageSize: 13 }) // 假设从外部传入





function handlePage(page) {
  pageData.value.page = page
  getLanePainList()
}

const addSort = (type) => {
  edit.value.showEdit(type)
}
function Valid(row) {


  return dateFormat(row.valid)

}

//一键新增货道
function oneKey() {
  onekey.value.showAdd()
}

function getLanePainList() {
  LanePainList(pageData.value).then(res => {
    let data = res.data
    if (data.code == 0) {
      LaneData.value = data.data.data
      pageData.value.total = data.data.total
      pageData.value.page = data.data.current_page
      pageData.value.pageSize = data.data.per_page
    }
  })
}

const Confirm = (title: string, msg: string, fun: any) => {
  ElMessageBox.confirm(`${msg}`, `${title}`, {
    distinguishCancelAndClose: true,
    confirmButtonText: '确定删除',
    cancelButtonText: '取消'
  })
    .then(() => {
      console.log("删除成功");
      fun()
    })
    .catch(() => {
      console.log("取消删除");

    })
}

function editFreightLane(id) {
  edit.value.showEdit('edit', id)

}

function editBalanceHeight(id) {
  balanceHeight.value.showEdit('edit', id)

}
function deleteFreightLane(id) {
  Confirm("删除货道信息", "是否确认删除该货道信息", () => {
    delFreightLane(id).then((res) => {
      let data = res.data
      if (data.code == 0) {
        getLanePainList()
      }
    })
  })
}

onMounted(() => {

  getLanePainList()
})

</script>

<style lang="scss" scoped>
@import './index';
</style>