<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" width="880px" close-icon="false" center
    :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form ref="form" :model="freightLaneData" :rules="rules" label-width="120px" label-position="right" inline>
      <el-form-item label="货道名称" prop="lane_num">
        <el-select v-model="freightLaneData.lane_num" class="m-select-2" placeholder="请选择" size="default"
          style="width: 200px" @change="ChangeLaneName" disabled>
          <el-option v-for="item in LaneNumber" :key="item.key" :label="item.key" :value="item.value" />
        </el-select>

        <el-select v-model="freightLaneData.line_num" label="line_num" class="m-select-2" placeholder="请选择"
          size="default" style="width: 100px" @change="ChangeLineName" disabled>
          <el-option v-for="item in LineNumber" :key="item.key" :label="item.key" :value="item.value" />
        </el-select>

      </el-form-item>
      <el-form-item label="疫苗名称" prop="vaccine_name">
        <el-autocomplete v-model.trim="freightLaneData.vaccine_name" :fetch-suggestions="vaccinumSearch" clearable
          value-key="vaccine_name" class="input_258" placeholder="选择或输入" @select="vaccineSelect" />
      </el-form-item>

      <el-form-item label="企业名称" prop="enterprise_name">
        <el-autocomplete v-model.trim="freightLaneData.enterprise_name" :fetch-suggestions="enterpriseSearch" clearable
          value-key="enterprise_name" class="input_258" placeholder="选择或输入" @select="enterpriseSelect" />
      </el-form-item>

      <!-- <el-form-item label="疫苗规格" prop="spec">
        <el-autocomplete v-model.trim="freightLaneData.spec" :fetch-suggestions="specSearch" clearable value-key="spec"
          class="input_258" placeholder="选择或输入" @select="specSelect" />
      </el-form-item> -->

      <el-form-item label="批 号" prop="batch_num">
        <el-autocomplete v-model.trim="freightLaneData.batch_num" :fetch-suggestions="batchSearch" clearable
          value-key="batch_num" class="input_258" placeholder="选择或输入" @select="batchSelect" />
      </el-form-item>
      <el-form-item label="有效期">
        <el-date-picker v-model.number="freightLaneData.valid" type="date" class="input_258" disabled
          format="YYYY/MM/DD" value-format="X" />
      </el-form-item>



    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClear" type="default">清空信息</el-button>
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(form)">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import { ref, defineEmits, onMounted } from 'vue';
import { getVaccineForBatch, getManufactorForBatch, getSpecForBatch, getBatchForBatch } from '@/api/batch'
import { addLaneInfo, getFreightLaneDetail, editLaneInfo } from '@/api/lane'
const emit = defineEmits(["refresh"])
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('增加货道信息')
const rules = ref({
  lane_num: [
    { required: true, message: '请选择货道名称', trigger: 'change' }
  ],
  vaccine_name: [
    { message: '请选择疫苗名称', trigger: 'change' }
  ],
  enterprise_name: [
    { message: '请选择企业名称', trigger: 'change' }
  ],
  spec: [
    { message: '请选择疫苗规格', trigger: 'change' }
  ],
  batch_num: [
    {
      message: '请选择批号', trigger: 'change'
    }
  ]
})

let dataInputType = ""
let dataInputId = 0
let vaccineName = ref([])
let manufactorName = ref([])
let specName = ref([])
let batchNum = ref([])

const LaneNumber = ref(
  [
    { key: "请选择", value: 0 },
    { key: "A", value: 1 },
    { key: "B", value: 2 },
    { key: "C", value: 3 },
    { key: "D", value: 4 },
    { key: "E", value: 5 },
    { key: "F", value: 6 },
    { key: "G", value: 7 },
    { key: "H", value: 8 },
    { key: "I", value: 9 },
    { key: "K", value: 10 },
    { key: "J", value: 11 },
    { key: "K", value: 12 },
    { key: "L", value: 13 },
    { key: "M", value: 14 },
    { key: "N", value: 15 },
    { key: "O", value: 16 },
    { key: "P", value: 17 }
  ]
)
const LineNumber = ref(ref(
  [
    { key: "请选择", value: 0 },
    { key: "1", value: 1 },
    { key: "2", value: 2 },
    { key: "3", value: 3 },
    { key: "4", value: 4 },
    { key: "5", value: 5 },
    { key: "6", value: 6 },
    { key: "7", value: 7 },
    { key: "8", value: 8 },
    { key: "9", value: 9 },
    { key: "10", value: 10 },
    { key: "11", value: 11 },
    { key: "12", value: 12 },
    { key: "13", value: 13 },
    { key: "14", value: 14 },
    { key: "15", value: 15 },
    { key: "16", value: 16 },
    { key: "17", value: 17 },
    { key: "18", value: 18 },
    { key: "19", value: 19 },
    { key: "20", value: 20 },
  ]
))
const freightLaneData = ref({})

function close() {
  freightLaneData.value = {}

}

const showEdit = (type, id) => {
  if (type == 'edit') {
    title.value = '换苗'
    dataInputType = "edit"
    dataInputId = id
    FreightLaneDetail(id)
    dialogVisible.value = true
  } else {
    title.value = '新增货道信息'
    dataInputType = ""
    dialogVisible.value = true
  }
}




function ChangeLaneName(e) {
  if (e == 0) {

    return
  }
  let line_num = freightLaneData.value.line_num
  freightLaneData.value.line_name = `${LaneNumber.value[e].key}${line_num}`
}
function ChangeLineName(e) {
  if (e == 0) {
    return
  }
  let lane_num = LaneNumber.value[freightLaneData.value.lane_num].key
  freightLaneData.value.line_name = `${lane_num}${e}`


}

const vaccineSelect = (item: any) => {
  freightLaneData.value.vaccine_code = item.vaccine_code
  ManufactorForBatch(item.vaccine_code)
}
const enterpriseSelect = (item: any) => {
  freightLaneData.value.enterprise_code = item.enterprise_code
  freightLaneData.value.vaccinum_id = item.vaccinum_id

  BatchbyBatch(item.vaccinum_id)
}
const specSelect = (item: any) => {
  freightLaneData.value.spec = item.spec
  freightLaneData.value.vaccinum_height = item.vaccinum_height
  freightLaneData.value.vaccinum_id = item.vaccinum_id
}

const batchSelect = (item: any) => {
  freightLaneData.value.batch_num = item.batch_num
  freightLaneData.value.valid = item.valid
}
const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? vaccineName.value.filter(selectFilter(queryString, "vaccine_name")) : vaccineName.value
  cb(results)
}
const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? manufactorName.value.filter(selectFilter(queryString, "enterprise_name")) : manufactorName.value
  cb(results)
}
const specSearch = (queryString: string, cb) => {
  const results = queryString ? specName.value.filter(selectFilter(queryString, "spec")) : specName.value
  cb(results)
}

const batchSearch = (queryString: string, cb) => {
  const results = queryString ? batchNum.value.filter(selectFilter(queryString, "spec")) : batchNum.value
  cb(results)
}



const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}


function VaccineForBatch() {
  getVaccineForBatch().then(res => {
    let data = res.data
    if (data.code == 0) {
      vaccineName.value = data.data

    }
  })
}


function ManufactorForBatch(vaccineCode) {
  // let vaccineCode = freightLaneData.value.vaccine_code
  getManufactorForBatch(vaccineCode).then(res => {
    let data = res.data
    if (data.code == 0) {
      manufactorName.value = data.data
    }
  })
}

// function SpecForBatch(enterpriseCode) {
//   let vaccineCode = freightLaneData.value.vaccine_code
//   getSpecForBatch(vaccineCode, enterpriseCode).then(res => {
//     let data = res.data
//     if (data.code == 0) {
//       specName.value = data.data
//     }
//   })
// }


function BatchbyBatch(vaccinum_id) {
  // let vaccinum_id = freightLaneData.value.vaccinum_id
  getBatchForBatch(vaccinum_id).then(res => {
    let data = res.data
    if (data.code == 0) {
      batchNum.value = data.data
    }
  })
}

function addLane() {
  let data = freightLaneData.value
  addLaneInfo(data).then(res => {
    console.log(res, "ressss");

    let data = res.data
    if (data.code == 0) {
      dialogVisible.value = false
      emit("refresh")
    }
  })
}
function handleClear() {
  freightLaneData.value.enterprise_code = ""
  freightLaneData.value.enterprise_name = ""
  freightLaneData.value.vaccinum_id = null
  freightLaneData.value.vaccine_name = ""
  freightLaneData.value.batch_num = ""
  freightLaneData.value.spec = ""



}

const handleConfirm = async (formEl: FormInstance | undefined) => {

  if (!formEl) return

  await formEl.validate((valid, fields) => {
    if (valid) {

      if (dataInputType == 'edit') {
        if (dataInputId == 0) {
          return false
        }

        editLaneInfo(dataInputId, freightLaneData.value).then(res => {
          let data = res.data
          if (data.code == 0) {
            dialogVisible.value = false
            freightLaneData.value = {}
            emit("refresh")
          } else {
            Alert("提交失败！", "请检查录入信息是否有误")
          }
        })


      } else {
        addLane(freightLaneData.value).then(res => {

          let data = res.data
          if (data.code == 0) {
            dialogVisible.value = false
            freightLaneData.value = {}
            emit("refresh")
          } else {
            Alert("提交失败！", "请检查录入信息是否有误")
          }
        })

      }

    } else {
      console.log('error submit!', fields)
    }
  })

}

function FreightLaneDetail(id) {
  getFreightLaneDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      freightLaneData.value = data.data
    }
  })

}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}

defineExpose({
  showEdit,
})

onMounted(() => {
  VaccineForBatch()
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
