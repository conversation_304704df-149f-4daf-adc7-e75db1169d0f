<template>
  <el-dialog @close="close" v-model="dialogOneKey" :title="title" :width="480" close-icon="false" center
    style="width: 400px;height:270px">
    <el-form ref="form" :model="freightLaneData" label-width="120px" label-position="right" inline>
      <el-form-item label="货道架编号" prop="lane_num">

        <el-select v-model="freightLaneData.lane_num" class="m-select-2" placeholder="请选择" size="default"
          style="width: 100px" @change="ChangeLaneName">
          <el-option v-for=" item in LaneNumber " :key="item.key" :label="item.key" :value="item.value" />
        </el-select>

      </el-form-item>


    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogOneKey = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(freightLaneData)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, ElMessage, ElMessageBox } from 'element-plus'
import { ref, defineEmits, onMounted } from 'vue';
import { onKey } from '@/api/lane'
const emit = defineEmits(["refresh"])
const form = ref<FormInstance>()
const dialogOneKey = ref<boolean>(false)
const title = ref('一键新增货道')



const LaneNumber = ref(
  [
    { key: "请选择", value: 0 },
    { key: "A", value: 1 },
    { key: "B", value: 2 },
    { key: "C", value: 3 },
    { key: "D", value: 4 },
    { key: "E", value: 5 },
    { key: "F", value: 6 },
    { key: "G", value: 7 },
    { key: "H", value: 8 },
    { key: "I", value: 9 },
    { key: "K", value: 10 },
    { key: "J", value: 11 },
    { key: "K", value: 12 },
    { key: "L", value: 13 }
  ]
)

let freightLaneData = ref({
  lane_num: 0
})

function close() {
  freightLaneData = ref({ lane_num: 0 })

}

const showAdd = () => {
  dialogOneKey.value = true
}


defineExpose({
  showAdd,
})

function handleConfirm(data) {
  onKey(data)
    .then((res) => {
      let data = res.data
      if (data.code == 0) {
        ElMessage({
          showClose: true,
          message: '创建成功！',
          type: 'success',
        })
        dialogOneKey.value = false
        emit("refresh")
      } else {

        ElMessage({
          showClose: true,
          message: data.message,
          type: 'warning',
        })

      }

    })
}


</script>

<style lang="scss" scoped>
@import '../index';
</style>
