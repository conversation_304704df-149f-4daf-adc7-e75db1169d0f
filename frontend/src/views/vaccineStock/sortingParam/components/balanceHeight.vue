<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center style="min-height:500px;"
    :close-on-click-modal="false" :close-on-press-escape="false" width="950px">
    <el-row>
      <el-col>
        <el-form ref="form" :model="freightLaneData" label-width="120px" label-position="right" inline>


          <template #default>
            <el-form-item label="货道编号" prop="lane_num">
              <el-select v-model="freightLaneData.lane_num" class="m-select-2" placeholder="请选择" size="default"
                style="width: 100px" disabled>
                <el-option v-for="item in LaneNumber" :key="item.key" :label="item.key" :value="item.value" />
              </el-select>

              <el-select v-model="freightLaneData.line_num" label="line_num" class="m-select-2" placeholder="请选择"
                size="default" style="width: 100px" disabled>
                <el-option v-for="item in LineNumber" :key="item.key" :label="item.key" :value="item.value" />
              </el-select>

            </el-form-item>
            <el-form-item label="RFID标签" prop="rfid" inline>
              <el-input v-model.number="freightLaneData.rfid" placeholder="请输入" clearable
                clasgetFreightLaneDetails="input_258" />
            </el-form-item>

            <el-form-item label="货道净空高度" prop="height">
              <el-input v-model.number="freightLaneData.height" clearable style="width: 100px" placeholder="请输入" />（mm）
            </el-form-item>

            <el-form-item label="配重高度" prop="balance_height">
              <el-input-number v-model="freightLaneData.balance_height" :precision="2" :step="0.01" :max="50" />
            </el-form-item>
            <el-form-item>
              <el-button type="warning" @click="heightMeasureLaser" :loading="has_load"
                style="width: 150px">测量货道净空高度</el-button>
            </el-form-item>



            <div style="display: flex;">
              <b>模型构建参数(最少采集4个采集的建议采集数量25%,50%,75%,100%)</b>
              <div style="margin-left:10px">
                <el-button type="primary" @click="addDataPoint" :loading="has_load"
                  style="width:100px">新增数据点</el-button>
              </div>
            </div>
            <div class="radius"
              style="border-radius:10px;border:1px solid #f3f3f3;margin-bottom: 3rem;max-height: 250px;overflow-y: auto;">
              <div id="points" class="points-list" style="max-height: 500px ;min-height: 100px">
                <div class="points-item" v-for="item, index in DataPoints" :key="item.num">
                  <div class="points-warp">
                    <div>
                      <span>测量数量：</span><el-input v-model.number="item.points_num" style="width: 60px;"
                        placeholder="数量" />
                    </div>
                    <div style="margin-left: 1rem;">
                      <span>高度数据：</span><el-input v-model.number="item.points_height" style="width: 60px;"
                        placeholder="点击测量" />(mm)
                    </div>
                    <div style="margin-left: 1rem;">
                      <el-button type="success" @click="measureLaser(index)" :loading="has_load">测量</el-button>
                    </div>

                  </div>
                </div>
              </div>


            </div>
          </template>







        </el-form>
      </el-col>
    </el-row>
    <el-row>

      <el-col :span="4">

      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close" type="default" :loading="has_load">取消</el-button>
        <el-button type="primary" @click="handleSave(form)" :loading="has_load">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, ElMessageBox, ElMessage } from 'element-plus'
import { ref, defineEmits, onMounted } from 'vue';
import { addLaneInfo, getFreightLaneDetail, editLaneInfo } from '@/api/lane'
import { getPointDataList, savePointData } from '@/api/dataPoints'
import { laserLineHeight } from '@/api/ipc'
const emit = defineEmits(["refresh"])
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('增加货道信息')
const has_load = ref(false)
const DataPoints = ref([])
let dataInputType = ""
let dataInputId = 0

const LaneNumber = ref(
  [
    { key: "请选择", value: 0 },
    { key: "A", value: 1 },
    { key: "B", value: 2 },
    { key: "C", value: 3 },
    { key: "D", value: 4 },
    { key: "E", value: 5 },
    { key: "F", value: 6 },
    { key: "G", value: 7 },
    { key: "H", value: 8 },
    { key: "I", value: 9 },
    { key: "K", value: 10 },
    { key: "J", value: 11 },
    { key: "K", value: 12 },
    { key: "L", value: 13 },
    { key: "M", value: 14 },
    { key: "N", value: 15 },
    { key: "O", value: 16 },
    { key: "P", value: 17 }
  ]
)
const LineNumber = ref(ref(
  [
    { key: "请选择", value: 0 },
    { key: "1", value: 1 },
    { key: "2", value: 2 },
    { key: "3", value: 3 },
    { key: "4", value: 4 },
    { key: "5", value: 5 },
    { key: "6", value: 6 },
    { key: "7", value: 7 },
    { key: "8", value: 8 },
    { key: "9", value: 9 },
    { key: "10", value: 10 },
    { key: "11", value: 11 },
    { key: "12", value: 12 },
    { key: "13", value: 13 },
    { key: "14", value: 14 },
    { key: "15", value: 15 },
    { key: "16", value: 16 },
    { key: "17", value: 17 },
    { key: "18", value: 18 },
    { key: "19", value: 19 },
    { key: "20", value: 20 },
  ]
))

const freightLaneData = ref({})

function close() {
  freightLaneData.value = {}
  DataPoints.value = []
  dialogVisible.value = false

}

const showEdit = (type, id) => {
  if (type == 'edit') {
    title.value = '设置货道信息'
    dataInputType = "edit"
    dataInputId = id
    FreightLaneDetail(id)

    dialogVisible.value = true
  } else {
    title.value = '新增货道信息'
    dataInputType = ""
    dialogVisible.value = true
  }
}
function addDataPoint() {
  DataPoints.value.push({ lane_num: freightLaneData.value.lane_num, line_num: freightLaneData.value.line_num, points_num: 0, points_height: 0 })
}


function addLane() {
  async function addLane() {
    let data = freightLaneData.value;
    try {
      const res = await addLaneInfo(data);

      if (res.data.code === 0) {
        dialogVisible.value = false;
        emit("refresh");
      }
      return res; // 返回 Promise
    } catch (error) {
      console.error(error);
      ElMessage({
        message: '提交失败！请检查录入信息是否有误',
        type: 'error',
      });
    }
  }
}
const handleSave = async (formEl: FormInstance | undefined) => {
  ElMessageBox.confirm(
    '是否确定保存该货道信息',
    '信息',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      handleConfirm(formEl)

    })
    .catch(() => {

    })
}
const savePoints = async () => {
  if (DataPoints.value.length < 4) {
    ElMessage({
      message: '请至少添加4个数据点！',
      type: 'warning',
    })
    return false
  }

  savePointData(DataPoints.value).then(res => {
    if (res.data.code == 0) {
      ElMessage({
        message: '保存成功',
        type: 'success'
      })
      freightLaneData.value = {}
      dialogVisible.value = false
      emit("refresh")
    } else {
      ElMessage({
        message: '保存失败！',
        type: 'error'
      })
    }

  })

}
const handleConfirm = async (formEl: FormInstance | undefined) => {

  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {

      if (dataInputType == 'edit') {
        if (dataInputId == 0) {
          return false
        }

        editLaneInfo(dataInputId, freightLaneData.value).then(res => {
          let data = res.data
          if (data.code == 0) {


            savePoints()

          } else {
            Alert("提交失败！", "请检查录入信息是否有误")
          }
        })


      } else {
        addLane()
      }

    } else {
      console.log('error submit!', fields)
    }
  })

}

function FreightLaneDetail(id) {
  getFreightLaneDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      freightLaneData.value = data.data
      GetPointData(freightLaneData.value)
    }
  })

}

function GetPointData(data) {
  getPointDataList(data).then(res => {
    let data = res.data
    if (data.code == 0) {
      DataPoints.value = data.data
    }
  })
}


const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {

    autofocus: true,
    confirmButtonText: 'OK',

  })
}
function heightMeasureLaser() {
  ElMessageBox.confirm(
    '请确保货道已清空，是否开始测量？',
    '信息',
    {
      confirmButtonText: '测量',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    has_load.value = true
    laserLineHeight(freightLaneData.value)
      .then(res => {
        let { code, data, message } = res.data
        if (code == 0) {
          freightLaneData.value.height = data.laser_height
          has_load.value = false
          ElMessage({
            message: '测量完成',
            type: 'success',
          })
        } else {
          ElMessage({
            message: '测量失败！' + message,
            type: 'error',
          })
        }
      })
      .catch(res => {
        ElMessage({
          message: '测量失败！',
          type: 'error',
        })
      })
      .finally(res => {
        has_load.value = false
      })

  })
}
function measureLaser(idx) {

  if (freightLaneData.value.height <= 0) {
    ElMessage({
      message: '请先测量货道净空高度！',
      type: 'error',
    })
    return false;
  }
  has_load.value = true
  laserLineHeight(freightLaneData.value)
    .then(res => {
      let { code, data, message } = res.data
      if (code == 0) {
        var laser_height = data.laser_height
        DataPoints.value[idx].points_height = laser_height



        has_load.value = false
        ElMessage({
          message: '测量计算成功',
          type: 'success',
        })
      } else {
        ElMessage({
          message: '测量计算失败！' + message,
          type: 'error',
        })
      }
    })
    .catch(res => {


      ElMessage({
        message: '测量计算失败！' + res,
        type: 'error',
      })
    })
    .finally(res => {
      has_load.value = false
    })
}
defineExpose({
  showEdit,
})

onMounted(() => {

})
</script>

<style lang="scss" scoped>
@import '../index';

.points-list {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  padding-bottom: 2rem;
}

.points-item {
  flex: 0 0 48%;
  max-width: 49%;
  min-width: 390px;
  box-sizing: border-box;
}

@media (max-width: 600px) {
  .points-item {
    flex-basis: 100%;
    max-width: 100%;
  }
}

.points-warp {
  display: flex;
  height: 40px;
  flex-direction: row;
}

.points-warp div {
  line-height: 40px;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
