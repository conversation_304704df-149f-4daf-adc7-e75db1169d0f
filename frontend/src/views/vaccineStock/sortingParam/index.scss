.tab-bar-container {
    .tar-bar {
        cursor: pointer;
        position: absolute;
        right: -50px;
        width: 285px;
        z-index: 99;
        height: 136px;
        background: url("@/assets/image/tarbar.png") no-repeat center center;
        background-size: 70%;
        display: flex;
        align-items: center;
        align-self: center;
        justify-content: center;
        top: -20px;

        .name {
            margin-left: 30px;
            width: 136px;
            height: 47px;
            font-size: 20px;
            // font-family: YouSheBiaoTiHei;
            color: #f69813;
            line-height: 47px;
            font-weight: bold;
        }
    }

    .tar-bar-calibration {
        cursor: pointer;
        position: absolute;
        background: #d2e5ea;
        width: 160px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        color: #f69813;
        font-size: 20px;
        font-weight: bold;
        border-radius: 30px;
        border: 4px solid #3a859a;
        top: 42px;
        right: 302px;
    }
}

.el-button--primary {
    width: 80px;
    height: 34px;
    background: #1677ff;
    border-radius: 6px;
    font-size: 14px;
}

.container-bg {
    width: calc(100vw - 80px);
    height: 800px;
    background: rgba(250, 250, 250, 1);
    border-radius: 8px;
    margin-top: 115px;

    .line {
        width: 100%;
        height: 12px;
        background: #fafafa;
        box-shadow:
            inset -1px 0px 0px 0px #f0f0f0,
            inset 0px -1px 0px 0px #f0f0f0,
            inset 1px 0px 0px 0px #f0f0f0;
        border-radius: 0px 0px 8px 8px;
    }

    .page {
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: end;
        margin-right: 20px;
    }

    .table {
        height: 740px;
    }
}

.m-select-2 {
    width: 20px;
}