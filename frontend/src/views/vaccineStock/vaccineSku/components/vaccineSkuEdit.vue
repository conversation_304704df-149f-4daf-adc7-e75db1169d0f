<template>
  <el-dialog
    @close="close"
    v-model="dialogVisible"
    :title="title"
    close-icon="false"
    center
  >
    <el-form
      ref="form"
      :model="vaccineForm"
      :rules="rules"
      label-width="120px"
      label-position="right"
      inline
    >
      <el-form-item label="疫苗名称">
        <el-select
          v-model="vaccineForm.vaccine_id"
          placeholder="请选择"
          clearable
          class="input_258"
        >
          <el-option
            v-for="(item, index) in vaccineList"
            :label="item.name"
            :value="item.id"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="疫苗码">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
      <el-form-item label="企业名称">
        <el-select
          v-model="vaccineForm.vaccine_id"
          placeholder="请选择"
          clearable
          class="input_258"
        >
          <el-option
            v-for="(item, index) in vaccineList"
            :label="item.name"
            :value="item.id"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业码">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>

      <el-form-item label="企业批号">
        <el-select
          v-model="vaccineForm.vaccine_id"
          placeholder="请选择"
          clearable
          class="input_258"
        >
          <el-option
            v-for="(item, index) in vaccineList"
            :label="item.name"
            :value="item.id"
            :key="index"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="疫苗规格">
        <el-select
          v-model="vaccineForm.vaccine_id"
          placeholder="请选择"
          clearable
          class="input_258"
        >
          <el-option
            v-for="(item, index) in vaccineList"
            :label="item.name"
            :value="item.id"
            :key="index"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="有效日期">
        <el-date-picker
          style="width: 258px"
          v-model="vaccineForm.date"
          type="date"
          placeholder="选择时间"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item label="疫苗数量">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_210" />
        <span class="w_48">支</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(form)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus'
import { ref, reactive } from 'vue'
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('疫苗库存修改')
const rules = reactive({})
const vaccineList = []
const vaccineForm = reactive({
  username: '',
  date: '',
  vaccine_id: '',
})

function close() {
  form.value.resetFields()
}

const showEdit = (type) => {
  dialogVisible.value = true
}

const handleConfirm = async (done: () => void) => {
  await form.value.validate((valid, fields) => {
    if (valid) {
      dialogVisible.value = false
      console.log('submit!', ruleForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}

defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>


