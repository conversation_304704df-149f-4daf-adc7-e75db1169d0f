<template>
  <full-container :isSHow="false" :title="vaccineTitle">
    <div class="tab-bar-container">
      <div class="tar-bar">
        <text class="name" @click="addVaccinde('add')">疫苗入库</text>
      </div>
    </div>
    <div class="container-bg">
      <el-table :data="vaccineData" border style="width: 100%" class="table">
        <el-table-column label="编号" width="65" type="index" />
        <el-table-column label="疫苗名称" width="200" prop="name" />
        <el-table-column label="疫苗码" width="160" />
        <el-table-column label="企业名称" width="200" prop="name" />
        <el-table-column label="批号" width="200" />
        <el-table-column label="有效期" width="200" />
        <el-table-column label="规格" width="200" />
        <el-table-column label="状态" width="160" />
        <el-table-column label="库存数量（支）" width="200" />
        <el-table-column label="操作">
          <template #default="scope">
            <span class="edit-link">冻结</span>
            <span class="edit-link">待核查</span>
            <span class="edit-link">复位</span>
            <span class="del-link">报废</span>
            <span class="edit-link">编辑</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <page-ination :page="pageData" @handle-page="handlePage" />
      </div>
    </div>

    <Edit ref="edit" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import Edit from './components/vaccineSkuEdit.vue'
const edit = ref()
const vaccineTitle = ref<String>('疫苗库存列表')
const vaccineData = [
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
]
const pageData = {
  total: 30,
  page: 1,
  page_size: 10,
}
function handlePage(page) {
  console.log(page)
  pageData.page = page
}

const addVaccinde = (type) => {
  edit.value.showEdit(type)
}
</script>

<style lang="scss" scoped>
@import './index';
</style>