<template>
  <full-container :isSHow="false" :title="vaccineTitle">
    <div class="tab-bar-container">
      <div class="tar-bar" @click="addManufactorName">
        <text class="name">新增批号信息</text>
      </div>
    </div>
    <div class="filtrate-box">
      <div class="filtrate">
        <div class="select-item">
          <b>批号：</b>
          <el-input v-model.trim="selectBatchNum" clearable value-key="batch_num" class="input_258" placeholder="输入批号"
            @keyup.enter="BatchList" @change="batchSelect" />

        </div>
        <div class="select-item">
          <b>疫苗名称：</b>
          <el-autocomplete v-model.trim="selectVaccineName" :fetch-suggestions="vaccinumSearch" clearable
            value-key="vaccine_name" class="input_258" placeholder="选择或输入" @select="vaccineSelect"
            @keyup.enter="BatchList" @change="vaccineSelect" />

        </div>
        <div class="select-item">
          <b>生产企业：</b>
          <el-autocomplete v-model.trim="selectManufacturer" :fetch-suggestions="enterpriseSearch" clearable
            value-key="enterprise_name" class="input_258" placeholder="选择或输入" @select="enterpriseSelect"
            @change="enterpriseSelect" @keyup.enter="BatchList" />

        </div>
      </div>
      <el-button type="default" class="filtrate-button" @click="filtrateReset">重置</el-button>
      <el-button type="primary" class="filtrate-button" @click="BatchList">查询</el-button>
    </div>
    <div class="container-bg">
      <div class="table-box">
        <el-table :data="manufactorData" border style="width: 100%" class="table">
          <el-table-column label="序号" width="80" type="index" />
          <el-table-column label="疫苗名称" min-width="240" prop="vaccine_name" />
          <el-table-column label="疫苗码" min-width="90" prop="vaccine_code" />
          <el-table-column label="生产企业" min-width="120" prop="enterprise_name" />
          <el-table-column label="疫苗规格" min-width="150" prop="spec" />
          <el-table-column label="是否多人份" min-width="100" prop="is_multi" :formatter="formatIsmulti" />
          <el-table-column label="批号" min-width="200" prop="batch_num" />
          <el-table-column label="所在货道" min-width="90" :formatter="formatLane" />
          <el-table-column label="有效期" min-width="150" prop="valid" :formatter="Valid" />
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="scope">
              <el-button size="default" type="primary" @click="editManufactorName(scope.row.id)">编辑</el-button>
              <el-button size="default" type="danger" @click="delManufactorName(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
            layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
        </div>
      </div>

    </div>

    <Edit ref="edit" @refresh="BatchList" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import Edit from './components/batchEdit.vue'
import { dateFormat } from '@/utils/date'
import { getBatchList, deleteBatch } from '@/api/batch'
import { getVaccinumfromManufacturer, getVaccinumfromVaccineName } from '@/api/vaccinum'
var Manufacturer = ref([])
var VaccineName = ref([])
var selectBatchNum = ref("")
var selectVaccineName = ref("")
var selectManufacturer = ref("")
var selectVaccineCode = ref("")
var selectManufacturerCode = ref("")

const edit = ref()
const vaccineTitle = ref<String>('疫苗批号管理')


let manufactorData = ref([]);
let pageData = reactive({
  total: 0,
  page: 1,
  pageSize: 10

})
function filtrateReset() {
  selectBatchNum.value = ""
  selectVaccineName.value = ""
  selectManufacturer.value = ""
  selectManufacturerCode.value = ""
  selectVaccineCode.value = ""
  pageData.page = 1
  BatchList()


}


function formatIsmulti(row) {
  return row.is_multi == 1 ? '是' : '否'
}


function formatLane(row) {
  let Lane = ["A", "B", "C", "D", "E", "F", "G"]
  let lane = row.lane_num
  let line = row.line_num

  if (lane == "") {
    return ""
  }

  return `${Lane[lane - 1]}${line}`
}
function Valid(row) {
  return dateFormat(row.valid)

}

const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? VaccineName.value.filter(selectFilter(queryString, "vaccine_name")) : VaccineName.value
  cb(results)
}

const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? Manufacturer.value.filter(selectFilter(queryString, "enterprise_name")) : Manufacturer.value
  // call callback function to return suggestion objects
  cb(results)
}


const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}

const batchSelect = (item: any) => {

  selectBatchNum.value = item || ""
}
const vaccineSelect = (item: any) => {

  selectVaccineCode.value = item.vaccine_code || ""
}
const enterpriseSelect = (item: any) => {
  selectManufacturerCode.value = item.enterprise_code || ""
}

function VaccinumfromManufacturer() {
  getVaccinumfromManufacturer().then((res) => {
    let data = res.data
    if (data.code == 0) {
      Manufacturer.value = data.data
    }
  })
}

function VaccinumfromVaccineName() {
  getVaccinumfromVaccineName().then((res) => {
    let data = res.data
    if (data.code == 0) {
      VaccineName.value = data.data
    }
  })
}

function handlePage(page) {
  pageData.page = page
  BatchList()
}
const addManufactorName = () => {
  edit.value.showEdit('add')
}

const editManufactorName = (id) => {
  edit.value.showEdit('edit', id)

}

const delManufactorName = (id) => {
  Confirm("删除疫苗批号", "是否确认删除该疫苗批号", () => {
    console.log(id);
    deleteBatch(id).then((res) => {
      let data = res.data
      if (data.code == 0) {
        BatchList()
      }
    })
  })
}


function BatchList() {

  getBatchList({ ...pageData, batch_num: selectBatchNum.value, vaccine_code: selectVaccineCode.value, enterprise_code: selectManufacturerCode.value }).then((res) => {

    let data = res.data
    if (data.code == 0) {
      pageData.total = data.data.total
      pageData.page = data.data.current_page
      pageData.pageSize = data.data.per_page
      manufactorData.value = data.data.data
    }

  })

}


const Confirm = (title: string, msg: string, fun: any) => {
  ElMessageBox.confirm(`${msg}`, `${title}`, {
    distinguishCancelAndClose: true,
    confirmButtonText: '确定删除',
    cancelButtonText: '取消'
  })
    .then(() => {
      console.log("删除成功");
      fun()
    })
    .catch(() => {
      console.log("取消删除");

    })
}


onMounted(() => {
  BatchList()
  VaccinumfromManufacturer()
  VaccinumfromVaccineName()
})
</script>

<style lang="scss" scoped>
.input_258 {
  width: 160px;
}

@import './index';
</style>