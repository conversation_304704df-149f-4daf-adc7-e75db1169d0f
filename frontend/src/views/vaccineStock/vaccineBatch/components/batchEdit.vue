<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center
    :close-on-click-modal="false">
    <el-form :model="batchForm" :rules="rules" label-width="120px" label-position="right" inline>
      <el-form-item label="疫苗名称" required>
        <el-autocomplete v-model.trim="batchForm.vaccine_name" :disabled="dataInputType === 'edit'"
          :fetch-suggestions="vaccinumSearch" clearable value-key="vaccine_name" class="input_258" placeholder="选择或输入"
          @select="vaccineSelect" />
      </el-form-item>
      <el-form-item label="疫苗码">
        <el-input v-model.trim="batchForm.vaccine_code" disabled clearable class="input_258" />
      </el-form-item>

      <el-form-item label="厂商名称" required>
        <el-autocomplete v-model.trim="batchForm.enterprise_name" :disabled="dataInputType === 'edit'"
          :fetch-suggestions="enterpriseSearch" clearable value-key="enterprise_name" class="input_258"
          placeholder="选择或输入" @select="enterpriseSelect" />
      </el-form-item>

      <el-form-item label="企业码">
        <el-input v-model.trim="batchForm.enterprise_code" disabled clearable class="input_258" />
      </el-form-item>

      <el-form-item label="疫苗批号" required>
        <el-input v-model.trim="batchForm.batch_num" clearable class="input_258" />
      </el-form-item>

      <el-form-item label="疫苗规格">
        <el-autocomplete v-model.trim="batchForm.spec" :disabled="dataInputType === 'edit'"
          :fetch-suggestions="specSearch" clearable value-key="spec" class="input_258" placeholder="选择或输入"
          @select="specSelect" />
      </el-form-item>

      <el-form-item label="有效日期">
        <el-date-picker v-model.number="batchForm.valid" type="date" class="input_258" placeholder="选择有效日期"
          format="YYYY/MM/DD" value-format="X" />
      </el-form-item>



    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import { addBatch, editBatch, getBatchDetail, getVaccineForBatch, getManufactorForBatch, getSpecForBatch } from '@/api/batch'
import { ref, reactive, defineEmits, onMounted } from 'vue';

const dialogVisible = ref<boolean>(false)
const title = ref('新增批号信息')
const rules = reactive({})
const emit = defineEmits(["refresh"])

let vaccineName = ref([])
let manufactorName = ref([])
let specName = ref([])

let batchForm = ref({
  vaccinum_id: 0,
  vaccine_name: "",
  vaccine_code: "",
  enterprise_name: "",
  enterprise_code: "",
  batch_num: "",
  spec: "",
  valid: 0
})



function close() {
  batchForm = ref({})
}



const showEdit = (type, id) => {
  if (type == 'edit') {
    title.value = '修改批号信息'
    dataInputType = "edit"
    dataInputId = id
    BatchDetail(id)
  } else {
    title.value = '新增批号信息'
    dataInputType = ""
    dialogVisible.value = true
  }
}
let dataInputType = ""
let dataInputId = 0
const BatchDetail = (id) => {
  getBatchDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      batchForm.value = { ...data.data }
      dialogVisible.value = true
    }
  })
}





const handleConfirm = () => {


  if (dataInputType == 'edit') {
    if (dataInputId == 0) {
      return false
    }

    let postData = {
      batch_num: batchForm.value.batch_num,
      vaccinum_id: batchForm.value.vaccinum_id,
      valid: batchForm.value.valid,

    }

    editBatch(dataInputId, postData).then(res => {
      let data = res.data
      if (data.code == 0) {
        dialogVisible.value = false
        batchForm = ref({})
        emit("refresh")
      } else {
        Alert("提交失败！", data.message)
      }
    })


  } else {
    addBatch(batchForm.value).then(res => {

      let data = res.data
      if (data.code == 0) {
        dialogVisible.value = false
        batchForm = ref({})
        emit("refresh")
      } else {
        Alert("提交失败！", data.message)
      }
    })

  }



}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}


const vaccineSelect = (item: any) => {
  batchForm.value.vaccine_code = item.vaccine_code
  ManufactorForBatch(item.vaccine_code)
}
const enterpriseSelect = (item: any) => {
  batchForm.value.enterprise_code = item.enterprise_code
  batchForm.value.vaccinum_id = item.vaccinum_id
  SpecForBatch(item.enterprise_code)
}

const specSelect = (item: any) => {
  batchForm.value.spec = item.spec
  batchForm.value.vaccinum_id = item.vaccinum_id
}
const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? vaccineName.value.filter(selectFilter(queryString, "vaccine_name")) : vaccineName.value
  console.log(queryString, "queryStringqueryStringqueryString", results);
  cb(results)
}
const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? manufactorName.value.filter(selectFilter(queryString, "enterprise_name")) : manufactorName.value
  cb(results)
}

const specSearch = (queryString: string, cb) => {
  const results = queryString ? specName.value.filter(selectFilter(queryString, "spec")) : specName.value
  cb(results)
}



const selectFilter = (queryString, key) => {


  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}

function VaccineForBatch() {
  getVaccineForBatch().then(res => {
    let data = res.data
    if (data.code == 0) {
      console.log(data.data, "sdfsdfsdfsdfsdfs");

      vaccineName.value = data.data

    }
  })
}


function ManufactorForBatch(vaccineCode) {
  getManufactorForBatch(vaccineCode).then(res => {
    let data = res.data
    if (data.code == 0) {
      manufactorName.value = data.data
    }
  })
}

function SpecForBatch(enterpriseCode) {
  let vaccineCode = batchForm.value.vaccine_code
  getSpecForBatch(vaccineCode, enterpriseCode).then(res => {
    let data = res.data
    if (data.code == 0) {
      specName.value = data.data
    }
  })
}


onMounted(() => {
  VaccineForBatch()

})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
