.container-bg {
    width: calc(100vw - 80px);
    height: 710px;
    border-radius: 8px;
    margin-top: 230px;
    position: relative;
    display: flex;
    overflow: hidden;
    flex-direction: column;

    .table-box {
        flex: 24;
        width: 100%;
        flex-direction: row;
    }

    .table-nil-box {
        flex: 1;
    }

    .pagination {
        border-top: 4px solid #fafafa;
        padding-top: 10px;
        background: #fff;
        display: flex;
        justify-content: end;
        bottom: 10px;
        right: 0px;
        width: 100%;
        box-sizing: border-box;
        margin-right: 20px;
    }

    .el-pagination {
        margin-right: 24px;
        padding: 10px;
    }

    .table {
        height: 570px;
    }
}

.tab-bar-container {
    position: absolute;
    width: 100vw;
    top: 80px;

    :deep(.tar-bar) {
        cursor: pointer;
        position: absolute;
        z-index: 99;
        right: -8px;
        width: 285px;
        height: 136px;
        background: url("@/assets/image/tarbar.png") no-repeat center center;
        display: flex;
        align-items: center;
        align-self: center;
        justify-content: center;

        .name {
            margin-left: 30px;
            width: 180px;
            height: 47px;
            font-size: 28px;
            // font-family: YouSheBiaoTiHei;
            color: #f69813;
            line-height: 47px;
            font-weight: bold;
        }
    }
}



.filtrate-box {
    display: flex;
    position: absolute;
    top: 170px;
    left: 40px;

    .select-item {
        margin-right: 20px;

        b {
            font-size: 16px;
            height: 20px;
            line-height: 20px;
        }
    }

    .filtrate-button {
        margin-left: 10px;
    }

    .filtrate {
        display: flex;
    }
}