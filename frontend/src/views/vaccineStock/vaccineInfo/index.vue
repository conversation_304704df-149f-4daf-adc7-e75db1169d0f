<template>
  <full-container :isSHow="false" :title="vaccineTitle">
    <div class="tab-bar-container">
      <div class="tar-bar">
        <text class="name" @click="addVaccindeName()">新增疫苗名称</text>
      </div>
    </div>
    <div class="container-bg">
      <div class="table-box">
        <el-table :data="vaccineData" border style="width: 100%" class="table">
          <el-table-column label="序号" width="120" type="index" />
          <el-table-column label="疫苗名称" min-width="300" prop="vaccine_name" />
          <el-table-column label="疫苗码" min-width="150" prop="vaccine_code" />
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="scope">
              <el-button size="default" type="primary" @click="editVaccindeName(scope.row.id)">编辑</el-button>
              <el-button size="default" type="danger" @click="delVaccinumName(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
            layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
        </div>
      </div>

    </div>

    <Edit ref="edit" @refresh="VaccinumPaginList" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import Edit from './components/vaccineEdit.vue'
import { getVaccinumNamePaginList, deleteVaccinumName } from '@/api/vaccinum'
const edit = ref()
const vaccineTitle = ref<String>('疫苗名称录入')
let vaccineData = ref([]);
let pageData = reactive({
  total: 0,
  page: 1,
  pageSize: 12,
})

function handlePage(page) {
  pageData.page = page
  VaccinumPaginList()
}
const addVaccindeName = () => {
  edit.value.showEdit('add')
}

const editVaccindeName = (id) => {
  edit.value.showEdit('edit', id)

}

const delVaccinumName = (id) => {
  Confirm("删除疫苗名称", "是否确认删除该疫苗名称信息", () => {
    console.log(id);
    deleteVaccinumName(id).then((res) => {
      let data = res.data
      if (data.code == 0) {
        VaccinumPaginList()
      }
    })
  })
}


function VaccinumPaginList() {

  getVaccinumNamePaginList(pageData).then((res) => {

    let data = res.data
    if (data.code == 0) {
      pageData.total = data.data.total
      pageData.page = data.data.current_page
      pageData.pageSize = data.data.per_page
      vaccineData.value = data.data.data
    }

  })

}


const Confirm = (title: string, msg: string, fun: any) => {
  ElMessageBox.confirm(`${msg}`, `${title}`, {
    distinguishCancelAndClose: true,
    confirmButtonText: '确定删除',
    cancelButtonText: '取消'
  })
    .then(() => {
      console.log("删除成功");
      fun()
    })
    .catch(() => {
      console.log("取消删除");

    })
}


onMounted(() => {
  VaccinumPaginList()
})
</script>

<style lang="scss" scoped>
@import './index';
</style>