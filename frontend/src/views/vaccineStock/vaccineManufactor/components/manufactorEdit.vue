<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center
    :close-on-click-modal="false">
    <el-form :model="vaccineForm" :rules="rules" label-width="120px" label-position="right" inline>
      <el-form-item label="厂商名称">
        <el-input v-model.trim="vaccineForm.enterprise_name" clearable class="input_258" />

      </el-form-item>
      <el-form-item label="企业码">
        <el-input v-model.trim="vaccineForm.enterprise_code" clearable class="input_258" />
      </el-form-item>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox, ElMessage } from 'element-plus'
import { addManufactorName, getManufactorNameDetail, editManufactorName, deleteManufactorName } from '@/api/manufactor'
import { ref, reactive, defineEmits, onMounted } from 'vue';
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('新增厂商信息')
const rules = reactive({})
const emit = defineEmits(["refresh"])



let vaccineForm = ref({
  "enterprise_name": "",
  "enterprise_code": ""
})

function close() {
  vaccineForm.value = {
    "enterprise_name": "",
    "enterprise_code": ""
  }
}




const showEdit = (type, id) => {
  if (type == 'edit') {
    title.value = '修改厂商信息'
    dataInputType = "edit"
    dataInputId = id
    ManufactorNameDetail(id)
  } else {
    title.value = '新增厂商信息'
    dataInputType = ""
    dialogVisible.value = true
  }
}
let dataInputType = ""
let dataInputId = 0
const ManufactorNameDetail = (id) => {
  getManufactorNameDetail(id).then(res => {
    let data = res.data
    if (data.code == 0) {
      vaccineForm.value = { ...data.data }
      dialogVisible.value = true
    }
  })
}





const handleConfirm = async (done: () => void) => {


  if (vaccineForm.value.enterprise_name != "" || vaccineForm.value.enterprise_code != "") {

    if (dataInputType == 'edit') {
      if (dataInputId == 0) {
        return false
      }

      editManufactorName(dataInputId, vaccineForm.value).then(res => {
        let data = res.data
        if (data.code == 0) {
          dialogVisible.value = false
          vaccineForm.value = {
            "enterprise_name": "",
            "enterprise_code": ""
          }
          ElMessage.success('更改成功')
          emit("refresh")
        } else {
          ElMessage.error('请检查录入信息是否有误!!')
        }
      })


    } else {
      addManufactorName(vaccineForm.value).then(res => {

        let data = res.data
        if (data.code == 0) {
          dialogVisible.value = false
          vaccineForm.value = {
            "enterprise_name": "",
            "enterprise_code": ""
          }
          ElMessage.success('新增成功！')
          emit("refresh")
        } else {
          ElMessage.error(data.message)
        }
      })

    }


  } else {
    ElMessageBox.alert('请填写完整信息！', '提示', {
      confirmButtonText: '确定',
      type: 'warning',
      callback: action => {

      }
    })
  }

}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}


onMounted(() => {


})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
