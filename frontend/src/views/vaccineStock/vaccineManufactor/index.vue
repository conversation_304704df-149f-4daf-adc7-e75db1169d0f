<template>
  <full-container :isSHow="false" :title="vaccineTitle">
    <div class="tab-bar-container">
      <div class="tar-bar">
        <text class="name" @click="addManufactorName">新增厂商信息</text>
      </div>
    </div>
    <div class="container-bg">
      <div class="table-box">
        <el-table :data="manufactorData" border style="width: 100%" class="table">
          <el-table-column label="序号" width="120" type="index" />
          <el-table-column label="厂商名称" min-width="300" prop="enterprise_name" />
          <el-table-column label="企业码" min-width="150" prop="enterprise_code" />
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="scope">
              <el-button size="default" type="primary" @click="editManufactorName(scope.row.id)">编辑</el-button>
              <el-button size="default" type="danger" @click="delManufactorName(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
            layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
        </div>
      </div>

    </div>

    <Edit ref="edit" @refresh="manufactorPaginList" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { FormInstance, FormRules, ElMessageBox } from 'element-plus'
import Edit from './components/manufactorEdit.vue'
import { getManufactorName, deleteManufactorName } from '@/api/manufactor'
const edit = ref()
const vaccineTitle = ref<String>('疫苗厂商录入')


let manufactorData = ref([]);
let pageData = reactive({
  total: 0,
  page: 1,
  pageSize: 12,
})


function handlePage(page) {
  pageData.page = page
  manufactorPaginList()
}
const addManufactorName = () => {
  edit.value.showEdit('add')
}

const editManufactorName = (id) => {
  edit.value.showEdit('edit', id)

}

const delManufactorName = (id) => {
  Confirm("删除疫苗名称", "是否确认删除该疫苗名称信息", () => {
    console.log(id);
    deleteManufactorName(id).then((res) => {
      let data = res.data
      if (data.code == 0) {
        manufactorPaginList()
      }
    })
  })
}


function manufactorPaginList() {

  getManufactorName(pageData).then((res) => {

    let data = res.data
    if (data.code == 0) {
      pageData.total = data.data.total
      pageData.page = data.data.current_page
      pageData.pageSize = data.data.per_page
      manufactorData.value = data.data.data
    }

  })

}


const Confirm = (title: string, msg: string, fun: any) => {
  ElMessageBox.confirm(`${msg}`, `${title}`, {
    distinguishCancelAndClose: true,
    confirmButtonText: '确定删除',
    cancelButtonText: '取消'
  })
    .then(() => {
      console.log("删除成功");
      fun()
    })
    .catch(() => {
      console.log("取消删除");

    })
}


onMounted(() => {
  manufactorPaginList()
})
</script>

<style lang="scss" scoped>
@import './index';
</style>