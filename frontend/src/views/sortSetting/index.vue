<template>
    <full-container :isSHow="false" :title="Title">
        <div class="container-bg">
            <div class="setting-list">
                <div v-for="(item, index) in settingList" :key="index" class="vaccine-item" @click="hanleClick(item)">
                    <img :src="getSrc(item.image_url)" alt="" class="icon-img" />
                    <span class="name">{{ item.name }} </span>
                </div>
            </div>
        </div>
        <Edit ref="edit" />

    </full-container>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getSrc } from "@/utils/image"
import Edit from './components/paramSet.vue'
const edit = ref()
const router = useRouter()
const Title = ref<String>('AVM设置')

const settingList = [
    {
        image_url: 'allocate.png',
        name: '分拨参数设置',
        type: "uri",
        param: "/sortingLane/index"
    },
    {
        image_url: 'sortset.png',
        name: '一般参数设置',
        type: "func",
        param: "edit.value.showEdit()"
    },
    {
        image_url: 'sortset.png',
        name: 'AVM多人份提示设置',
        type: "uri",
        param: "/setTips/index"
    },
]

const hanleClick = (item) => {
    if (item.type == "uri") {
        router.push(item.param)
    } else if (item.type == "func") {
        eval(item.param)
    }


}
</script>

<style lang="scss" scoped>
@import './index';
</style>
