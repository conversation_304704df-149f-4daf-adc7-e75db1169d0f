<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center>
    <el-form
      ref="form"
      :model="paramForm"
      :rules="rules"
      label-width="150px"
      label-position="right"
      inline
    >
      <el-form-item label="多人份配送">
      <span class="input_210"><el-switch v-model="paramForm.status" inline-prompt active-text="开" inactive-text="关" /></span>  
      </el-form-item>
      <el-form-item label="传统带开关">
        <el-switch v-model="paramForm.status" inline-prompt active-text="开" inactive-text="关" />
      </el-form-item>
      <el-form-item label="重复叫号时间间隔">
        <el-input v-model.trim="paramForm.number" placeholder="请输入" class="input_210" clearable />
        <span class="w_48">盒</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(form)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance } from 'element-plus'
import { ref, reactive } from 'vue'
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('一般参数设置')
const rules = reactive({})
const vaccineList = []
const paramForm = reactive({
  number: '',
  status: true,
})

function close() {
  form.value.resetFields()
}

const showEdit = (type) => {
  dialogVisible.value = true
}

const handleConfirm = async (done: () => void) => {
  await form.value.validate((valid, fields) => {
    if (valid) {
      dialogVisible.value = false
      console.log('submit!', ruleForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}

defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>


