<template>
  <full-container :isSHow="false" :title="Title">
    <div class="tab-bar-container">
      <div class="tar-bar" @click="addAvm('add')">
        <text class="name">初始化接种台</text>
      </div>
    </div>
    <div class="container-bg">
      <el-form ref="form" :model="laneForm" label-width="150px" label-position="right" inline class="form-style">
        <el-form-item label="">
          <span class="lane-num">接种台数量：{{ total }}</span>
        </el-form-item>
        <el-form-item label="接种台运送顺序">
          <el-select v-model="laneForm.id" placeholder="请选择" clearable class="input_258">
            <el-option v-for="(item, index) in vaccineList" :label="item.name" :value="item.id" :key="index" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table :data="avmData" border style="width: 100%" class="table">
        <el-table-column label="编号" width="65" type="index" />
        <el-table-column label="接种台名称" prop="name" />
        <el-table-column label="AVM名称" prop="win_name" />
        <el-table-column label="AVM编号" prop="avm_num" />
        <!-- <el-table-column label="AVM触屏ID" prop="device_id" /> -->
        <!-- <el-table-column label="分拨器开关">
          <template #default="{ row }">
            <el-switch v-model="row.status" inline-prompt active-text="开" inactive-text="关" />
          </template>
</el-table-column> -->
        <!-- <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button type="primary">编辑</el-button>
            <el-button type="danger">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>

    </div>
    <Edit ref="edit" />
    <Add ref="add" @refresh="getAvmList" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Edit from './components/laneEdit.vue'
import Add from './components/add.vue'
import { AvmList } from '@/api/avm'
const laneForm = ref({
  username: '',
})
const total = ref(0)
const vaccineList = []
const edit = ref()
const add = ref()
const Title = ref<String>('分拨货道参数设置')
const avmData = ref([])


const addAvm = (type) => {
  add.value.showEdit(type)
}

function getAvmList() {
  AvmList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      total.value = data.data.total
      avmData.value = data.data.data
    }
  })
}

onMounted(() => {
  getAvmList()
})

</script>

<style lang="scss" scoped>
@import './index';
</style>
