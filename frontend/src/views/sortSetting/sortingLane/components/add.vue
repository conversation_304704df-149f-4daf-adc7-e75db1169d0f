<template>
  <el-dialog @close="close" v-model="avmAddVisible" :title="title" close-icon="false" center
    style="width: 400px;height: 200px;">
    <el-form ref="form" :model="AvmInfo" :rules="rules" label-width="120px" label-position="right" inline>
      <el-row justify="center" align="middle">
        <el-col :span="1">
          从
        </el-col>
        <el-col :span="4">
          <el-select v-model="avmAddStart">
            <template v-for="item, index in avmTotal" :key="index">
              <el-option :label="index" :value="index" v-if="index !== 0" />
            </template>
          </el-select>
        </el-col>
        <el-col :span="1">
          到
        </el-col>
        <el-col :span="4">
          <el-select v-model="avmAddEnd">
            <template v-for="item, index in avmTotal" :key="index">
              <el-option :label="index" :value="index" v-if="index !== 0" />
            </template>
          </el-select>
        </el-col>

      </el-row>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="avmAddVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { ElMessage, FormInstance } from 'element-plus'
import { AddAvm } from '@/api/avm'
import { ref, defineEmits } from 'vue'
const form = ref<FormInstance>()
const avmAddVisible = ref<boolean>(false)
const title = ref('初始化接种台')
const rules = ref({})
const avmTotal = 30
const avmAddStart = ref(1)
const avmAddEnd = ref(5)
const AvmInfo = ref({
  tableNum: '',
  avmName: '',
  avmNum: '',
})
const emit = defineEmits(["refresh"])
function close() {
  form.value.resetFields()
}

const showEdit = (type) => {
  avmAddVisible.value = true
}

const handleConfirm = async () => {
  try {
    let { data } = await AddAvm({ start: avmAddStart.value, end: avmAddEnd.value })

    if (data.code == 0) {
      ElMessage({
        showClose: true,
        message: "初始化成功！",
        type: 'success',
      })
    }
    emit("refresh")
    avmAddVisible.value = false




  } catch (e) {
    let { data, code, message } = e.response.data

    ElMessage({
      showClose: true,
      message: message,
      type: 'error',
    })


  }


}

defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>
