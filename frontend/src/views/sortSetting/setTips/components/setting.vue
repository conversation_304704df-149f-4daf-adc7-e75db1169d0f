<template>
  <el-dialog @close="close" v-model="dialogVisible" :title="title" close-icon="false" center
    :close-on-click-modal="false" :close-on-press-escape="false">
    <el-row>
      <el-col>
        <el-form :model="avmForm" label-width="110px" label-position="right" inline>
          <el-form-item label="疫苗名称" prop="vaccine_name">
            <el-autocomplete v-model.trim="avmForm.vaccine_name" :disabled="dataInputType === 'edit'"
              :fetch-suggestions="vaccinumSearch" clearable value-key="vaccine_name" class="input_210"
              placeholder="选择或输入" @select="vaccineSelect" />
          </el-form-item>
          <el-form-item label="疫苗码">
            <el-input v-model.trim="avmForm.vaccine_code" disabled clearable class="input_210" />
          </el-form-item>

          <el-form-item label="厂商名称" prop="enterprise_name">
            <el-autocomplete v-model.trim="avmForm.enterprise_name" :disabled="dataInputType === 'edit'"
              :focus="loadeEnterprise" :fetch-suggestions="enterpriseSearch" clearable value-key="enterprise_name"
              class="input_210" placeholder="选择或输入" @select="enterpriseSelect" />
          </el-form-item>

          <el-form-item label="企业码">
            <el-input v-model.trim="avmForm.enterprise_code" disabled clearable class="input_210" />
          </el-form-item>

          <el-form-item label="疫苗规格" prop="spec">
            <el-autocomplete v-model.trim="avmForm.spec" :disabled="dataInputType === 'edit'"
              :fetch-suggestions="specSearch" clearable value-key="spec" class="input_210" placeholder="选择或输入"
              @select="specSelect" />
          </el-form-item>
          <el-form-item label="是否多人份">
            <el-input type="text" v-model="avmForm.is_multi" disabled clearable class="input_210"
              :formatter="IsMultiFormatter" />
          </el-form-item>
          <el-form-item label="开封后过期时间">
            <el-input-number v-model="avmForm.time_over" :step="0.5" step-strictly />小时
          </el-form-item>
          <el-form-item label="到期前" label-width="100px">
            <el-input-number v-model="avmForm.after_time" :step="5" step-strictly />分钟AVM上提示
            <b style="color: red;">(0为不在avm上提示)</b>
          </el-form-item>

        </el-form>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" type="default">取消</el-button>
          <el-button type="primary" @click="handleConfirm">保存</el-button>
        </span>
      </el-col>
    </el-row>

  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance, FormRules, ElMessageBox, ElMessage } from 'element-plus'
import { addBatch, editBatch, getBatchDetail, getVaccineForBatch, getManufactorForBatch, getSpecForBatch } from '@/api/batch'
import { AddTips } from '@/api/avm'
import { ref, reactive, defineEmits, onMounted } from 'vue';
const ruleFormRef = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('新增AVM多人份信息')

const emit = defineEmits(["refresh"])
const vaccineName = ref([])
const manufactorName = ref([])
const specName = ref([])

const avmForm = ref({
  vaccine_name: "",
  vaccine_code: "",
  enterprise_name: "",
  enterprise_code: "",
  time_over: 1,
  after_time: 0,
  is_multi: 1,
  spec: "",
})


const dataInputType = ref("")
function close() {

  avmForm.value = {
    vaccine_name: "",
    vaccine_code: "",
    enterprise_name: "",
    enterprise_code: "",
    time_over: 1,
    after_time: 0,
    is_multi: 1,
    spec: "",
  }
}






function IsMultiFormatter(value) {
  return value == 1 ? "是" : "否"
}
function loadeEnterprise() {
  ManufactorForBatch()
}


const showEdit = (type, id) => {

  title.value = '新增AVM多人份提示信息'
  dataInputType.value = ""
  dialogVisible.value = true

}


const handleConfirm = async () => {
  if (avmForm.value.vaccine_name == "") {
    ElMessage.error("请选择疫苗名称")
  } else if (avmForm.value.enterprise_name == "") {
    ElMessage.error("请选择厂商名称")
  } else if (avmForm.value.spec == "") {
    ElMessage.error("请选择疫苗规格")
  } else {
    avmForm.value.time_over = avmForm.value.time_over * 60
    AddTips(avmForm.value).then(res => {
      let data = res.data
      if (data.code == 0) {
        avmForm.value = {
          vaccine_name: "",
          vaccine_code: "",
          enterprise_name: "",
          enterprise_code: "",
          time_over: 1,
          after_time: 0,
          is_multi: 1,
          spec: "",
        }
        dialogVisible.value = false
        emit("refresh")
      } else {
        Alert("提交失败！", "请检查录入信息是否有误")
      }


    })
  }







}
const Alert = (title: string, msg: string) => {
  ElMessageBox.alert(`${msg}`, `${title}`, {
    // if you want to disable its autofocus
    autofocus: true,
    confirmButtonText: 'OK',

  })
}


const vaccineSelect = (item: any) => {
  avmForm.value.vaccine_code = item.vaccine_code
}
const enterpriseSelect = (item: any) => {
  avmForm.value.enterprise_code = item.enterprise_code
  SpecForBatch(item.enterprise_code)
}

const specSelect = (item: any) => {
  avmForm.value.spec = item.spec
  avmForm.value.is_multi = item.is_multi
}
const vaccinumSearch = (queryString: string, cb) => {
  const results = queryString ? vaccineName.value.filter(selectFilter(queryString, "vaccine_name")) : vaccineName.value
  cb(results)
}
const enterpriseSearch = (queryString: string, cb) => {
  const results = queryString ? manufactorName.value.filter(selectFilter(queryString, "enterprise_name")) : manufactorName.value
  cb(results)
}

const specSearch = (queryString: string, cb) => {
  const results = queryString ? specName.value.filter(selectFilter(queryString, "spec")) : specName.value
  cb(results)
}



const selectFilter = (queryString, key) => {
  return (restaurant) => {
    return (
      restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) >= 0
    )
  }
}

function VaccineForBatch() {
  getVaccineForBatch().then(res => {
    let data = res.data
    if (data.code == 0) {
      vaccineName.value = data.data

    }
  })
}


function ManufactorForBatch() {
  let vaccineCode = avmForm.value.vaccine_code
  getManufactorForBatch(vaccineCode).then(res => {
    let data = res.data
    if (data.code == 0) {
      manufactorName.value = data.data
    }
  })
}

function SpecForBatch(enterpriseCode) {
  let vaccineCode = avmForm.value.vaccine_code
  getSpecForBatch(vaccineCode, enterpriseCode).then(res => {
    let data = res.data
    if (data.code == 0) {
      specName.value = data.data
    }
  })
}


onMounted(() => {
  VaccineForBatch()

})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';

.dialog-footer {
  margin-top: 60px;
}
</style>
