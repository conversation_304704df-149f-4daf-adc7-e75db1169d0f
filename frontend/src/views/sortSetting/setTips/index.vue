<template>
  <full-container :isSHow="false" :title="Title">
    <!-- <div class="container-bg"> -->
    <div class="laneBox">
      <el-row :gutter="10">
        <el-col :span="1">

        </el-col>
        <el-col :span="6">
          <el-button type="primary" size="large" @click="SetVaccine()">新增疫苗提示配置</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="tableHeight">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="vaccine_name" label="疫苗名称" width="180" />
            <el-table-column prop="vaccine_code" label="疫苗码" width="100" />
            <el-table-column prop="enterprise_name" label="厂家名称" width="180" />
            <el-table-column prop="enterprise_code" label="厂家码" width="100" />
            <el-table-column prop="is_multi" label="是否多人份" :formatter="formatIsmulti" width="100" />
            <el-table-column prop="spec" label="规格" />
            <el-table-column prop="time_over" label="开封后过期时间(分钟)" width="180" />
            <el-table-column prop="after_time" label="到期前提示(分钟)" width="180" />
            <el-table-column label="状态">
              <template #default="scope">
                <el-switch v-model="scope.row.active_status" size="large" inline-prompt active-text="启用"
                  inactive-text="停用" change="changeStatus" :inactive-value="2" :active-value="1"
                  style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                  @change="changeTipsActive(scope.row.id, scope.row.active_status)" />
              </template>
            </el-table-column>

            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="danger" @click="deleteAvmTips(scope.row)">删除</el-button>
              </template>

            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18"></el-col>
        <el-col :span="6">
          <!-- <div class="pagination">
            <el-pagination v-model:currentPage="pagination.page" :page-size="10" background
              layout="total, prev, pager, next, jumper" :total="pagination.total"
              @current-change="handleCurrentChange" />
          </div> -->
        </el-col>
      </el-row>
    </div>
    <!-- </div> -->


  </full-container>

  <Setting ref="setting" @refresh="getSettingList" />
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'

//当前使用的页面引入


import Setting from './components/setting.vue'

import { TipsList, DeleteTips, ChangeTipsActive } from '@/api/avm'
import { ElMessageBox } from 'element-plus';
const Title = ref<String>('AVM多人份提示设置')

const tableData = ref([])
const setting = ref()

const SetVaccine = () => {
  setting.value.showEdit("add")
}

function changeTipsActive(id, status) {

  ChangeTipsActive({ id: id, active_status: status })
    .then(res => {
      let { code, message, data } = res.data
      if (code == 0) {
        getSettingList()
      }

    })
}

function deleteAvmTips(row) {
  console.log(row, "row");

  ElMessageBox.confirm('此操作将永久删除提示配置, 是否确定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    DeleteTips(row.id)
      .then(res => {
        console.log(res, "dfdfdf");
        getSettingList()
      })
  })

}

const pagination = reactive({
  page: 1,
  pagesize: 10,
  total: 0,
})
function formatType(row, column) {

  return row.cron_type == 1 ? '每天' : '每周'

}
function formatIsmulti(row) {
  return row.is_multi == 1 ? '是' : '否'
}

function formatCron(row, column) {
  const [minutes, hours, dayOfMonth, month, dayOfWeek] = row.cron_time.split(' ');
  return `${hours}:${minutes}`;
}
function getSettingList() {
  TipsList()
    .then(res => {
      console.log(res);

      let { code, data, message } = res.data
      if (code == 0) {
        tableData.value = data
      }
    })
}
onMounted(() => {
  getSettingList()

})
</script>

<style lang="scss" scoped>
@use '../index.scss';

.laneBox {
  .el-row {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.lane-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.ipc-lane-box {
  display: flex;

  margin-bottom: 20px;
}

.ipc-lane-name {
  line-height: 32px;
  font-size: 16px;
  margin-right: 10px;
}

.el-radio-group {
  display: flex;
}

.lane-checkbox-warp {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: flex-start;

  .lane-checkbox-item {
    margin-right: 10px;

  }
}

.tableHeight {
  height: 630px;
}

.laneBox {
  background: #fff;
  margin: auto;
  width: 80%;
  height: 800px;
  border-radius: 20px;


  .laneBoxStep {
    padding: 30px;

  }

  .laneBoxButton {
    display: flex;
    padding-top: 100px;

    justify-content: center;
  }
}

.pagination {
  margin-top: 30px;
}
</style>
