<template>
    <full-container :isSHow="false" :title="Title">
        <div class="container-bg">
            <div class="setting-list">
                <div v-for="(item, index) in settingList" :key="index" class="vaccine-item" @click="hanleClick(item)">
                    <img :src="getSrc(item.image_url)" alt="" class="icon-img" />
                    <span class="name">{{ item.name }} </span>
                </div>
            </div>
        </div>


    </full-container>
    <Measure ref="measure" />
    <SingleMeasure ref="singlemeasure" />

</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getSrc } from "@/utils/image"
import Measure from './components/measure.vue'
import SingleMeasure from './components/singleMeasure.vue'
const measure = ref()
const singlemeasure = ref()
const router = useRouter()
const Title = ref<String>('货道设置')
const settingList = [
    {
        image_url: 'allocate.png',
        name: '测量所有货道净高度',
        type: "func",
        param: "measure.value.showEdit()"
    },
    {
        image_url: 'sortset.png',
        name: '单柜测量净高度',
        type: "func",
        param: "singlemeasure.value.showEdit()"
    },
    {
        image_url: 'sortset.png',
        name: '货道定时复核',
        type: "uri",
        param: "/freightLane/SetInterval"
    },
]

const hanleClick = (item) => {
    if (item.type == "uri") {
        router.push(item.param)
    } else if (item.type == "func") {
        eval(item.param)
    }


}
</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
