<template>
    <full-container :isSHow="false" :title="Title">
        <!-- <div class="container-bg"> -->
        <div class="laneBox">
            <el-row :gutter="10">
                <el-col :span="1">

                </el-col>
                <el-col :span="6">
                    <el-button type="primary" @click="changeAdd">新增任务</el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col class="tableHeight">
                    <el-table :data="tableData" style="width: 100%">
                        <el-table-column prop="id" label="任务编号" width="100" />
                        <el-table-column prop="cron_name" label="任务名称" width="180" />
                        <el-table-column prop="cron_type" label="定时周期" width="180" :formatter="formatType" />
                        <el-table-column prop="cron_time" label="定时时间" :formatter="formatCron" />
                        <el-table-column label="状态">
                            <template #default="scope">
                                <el-switch v-model="scope.row.active_status" size="large" inline-prompt active-text="启用"
                                    inactive-text="停用" change="changeStatus" :inactive-value="2" :active-value="1"
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                                    @change="cheangeSwitch(scope.row.id, scope.row.active_status)" />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-button type="primary"
                                    @click="changeEdit(scope.row.id, scope.row.cron_name, scope.row.cron_time)">编辑</el-button>
                                <el-button type="danger" @click="deleteCron(scope.row)">删除</el-button>
                            </template>

                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="18"></el-col>
                <el-col :span="6">
                    <div class="pagination">
                        <el-pagination v-model:currentPage="pagination.page" :page-size="10" background
                            layout="total, prev, pager, next, jumper" :total="pagination.total"
                            @current-change="handleCurrentChange" />
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- </div> -->


    </full-container>

    <CronTask ref="crontask" @refresh="getList" />


</template>
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'

//当前使用的页面引入

// import 'vue3-cron-plus/dist/index.css' // 引入样式

// import Vue3Cron from '@/components/CronForm/index.vue'
import CronTask from '@/components/CronTask/index.vue'
import { GetCronList, DeleteCron, ChangeCronActive } from '@/api/cron'
import { ElMessageBox } from 'element-plus';
const Title = ref<String>('货道定时复核')

const tableData = ref([])
const crontask = ref()
const expression = ref("")
const taskName = ref("")

const changeEdit = (id, name, time) => {
    crontask.value.showEdit("edit", id, name, time)
}

const changeAdd = () => {
    crontask.value.showEdit("add")
}

function cheangeSwitch(id, status) {

    ChangeCronActive({ id: id, active_status: status })
        .then(res => {
            let { code, message, data } = res.data
            if (code == 0) {
                getList()
            }

        })
}
const handleCurrentChange = (val: number) => {
    console.log(`current page: ${val}`)
    pagination.page = val
    getList()
}
function deleteCron(row) {
    ElMessageBox.confirm('此操作将永久删除该定时任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        DeleteCron(row.id)
            .then(res => {
                console.log(res);
                getList()
            })
    })

}

const pagination = reactive({
    page: 1,
    pagesize: 10,
    total: 0,
})
function formatType(row, column) {

    return row.cron_type == 1 ? '每天' : '每周'

}
function formatCron(row, column) {
    const [minutes, hours, dayOfMonth, month, dayOfWeek] = row.cron_time.split(' ');
    return `${hours}:${minutes}`;
}
function getList() {
    GetCronList(pagination)
        .then(res => {
            let { code, data, message } = res.data
            if (code == 0) {
                tableData.value = data.data
                pagination.total = data.total
                pagination.pagesize = data.per_page
            }
        })
}
onMounted(() => {
    getList()

})
</script>

<style lang="scss" scoped>
@use '../index.scss';

.laneBox {
    .el-row {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

.lane-title {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

.ipc-lane-box {
    display: flex;

    margin-bottom: 20px;
}

.ipc-lane-name {
    line-height: 32px;
    font-size: 16px;
    margin-right: 10px;
}

.el-radio-group {
    display: flex;
}

.lane-checkbox-warp {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: flex-start;

    .lane-checkbox-item {
        margin-right: 10px;

    }
}

.tableHeight {
    height: 630px;
}

.laneBox {
    background: #fff;
    margin: auto;
    width: 80%;
    height: 800px;
    border-radius: 20px;


    .laneBoxStep {
        padding: 30px;

    }

    .laneBoxButton {
        display: flex;
        padding-top: 100px;

        justify-content: center;
    }
}

.pagination {
    margin-top: 30px;
}
</style>
