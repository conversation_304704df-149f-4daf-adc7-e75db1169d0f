<template>
  <el-dialog :show-close="false" v-model="dialogVisible" :title="title" close-icon="false" center :width="500">
    <!-- <div class="dialogForm">
      <el-button type="primary" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">测量</el-button>
    </div> -->
    <h1 style="text-align: center;">功能暂无法使用，敬请期待</h1>
  </el-dialog>
</template>


<script lang="ts" setup>
import { ElMessageBox, FormInstance } from 'element-plus'
import { ref, reactive } from 'vue'
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('批量测量所有货道净高度')



const showEdit = (type) => {
  dialogVisible.value = true
}
const handleCancel = () => {
  dialogVisible.value = false
}
const handleConfirm = async () => {
  ElMessageBox.confirm('确认测量吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    ElMessageBox.alert('测量完成', '提示', {
      confirmButtonText: '确定',
      type: 'success',
    }).then(() => {
      dialogVisible.value = false
    })
  })
}

defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
.dialogForm {
  padding-top: 20px;
  display: flex;
  justify-content: center
}

@import '../index';
</style>
