// 电子标签管理页面样式

// 导航标签页样式
.nav-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e4e7ed;
    background: #fff;
    border-radius: 2px 2px 0 0;
    overflow: hidden;
    margin-top: 90px;

    .nav-tab {
        padding: 15px 30px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        color: #606266;
        font-weight: 500;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #f8f9fa;

        &:hover {
            color: #409eff;
            background: #ecf5ff;
        }

        &.active {
            color: #409eff;
            border-bottom-color: #409eff;
            background: #fff;
            font-weight: 600;
        }

        &:not(:last-child) {
            border-right: 1px solid #e4e7ed;
        }
    }
}

// 筛选和操作按钮容器
.tab-bar-container {
    margin-bottom: 20px;

    .filtrate-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .filtrate {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            flex: 1;

            .select-item {
                display: flex;
                align-items: center;

                b {
                    margin-right: 8px;
                    white-space: nowrap;
                    color: #333;
                    font-weight: 500;
                    font-size: 14px;
                }

                .input_258 {
                    width: 200px;
                }
            }
        }

        .filtrate-button {
            margin-left: 10px;
            min-width: 80px;
        }
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        padding: 0 20px;
    }
}

// 表格容器
.container-bg {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .table {
        margin-bottom: 20px;

        // 表格头部样式
        :deep(.el-table__header) {
            background-color: #f5f7fa;

            th {
                background-color: #f5f7fa !important;
                color: #333;
                font-weight: 600;
                font-size: 14px;
                border-bottom: 2px solid #e4e7ed;
            }
        }

        // 表格行样式
        :deep(.el-table__row) {
            &:hover {
                background-color: #f5f7fa;
            }

            td {
                border-bottom: 1px solid #ebeef5;
                font-size: 14px;
            }
        }

        // 操作按钮样式
        :deep(.el-button) {
            margin-right: 5px;

            &.el-button--small {
                padding: 5px 10px;
                font-size: 12px;
            }
        }
    }

    .pagination {
        display: flex;
        justify-content: center;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
    }
}

// 对话框样式
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    line-height: 1.4;
}

// 状态标签样式
.status-tag {
    &.online {
        color: #67c23a;
    }

    &.offline {
        color: #f56c6c;
    }

    &.bound {
        color: #409eff;
    }

    &.unbound {
        color: #909399;
    }

    &.low-battery {
        color: #e6a23c;
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .tab-bar-container {
        .filtrate-box {
            .filtrate {
                .select-item {
                    .input_258 {
                        width: 150px;
                    }
                }
            }
        }

        .action-buttons {
            justify-content: flex-start;
        }
    }
}

@media (max-width: 768px) {
    .nav-tabs {
        .nav-tab {
            padding: 12px 20px;
            font-size: 14px;
        }
    }

    .tab-bar-container {
        .filtrate-box {
            flex-direction: column;
            align-items: stretch;

            .filtrate {
                margin-bottom: 15px;

                .select-item {
                    margin-bottom: 10px;

                    .input_258 {
                        width: 100%;
                        max-width: 200px;
                    }
                }
            }
        }

        .action-buttons {
            justify-content: center;
        }
    }

    .container-bg {
        padding: 15px;

        .table {
            :deep(.el-table) {
                font-size: 12px;
            }
        }
    }
}