.container-bg {
    margin-top: 116px;
    height: 800px;
    overflow: hidden;
    border-radius: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    .container-item {
        height: 100%;
        width: calc(100% - 100px);
        display: flex;
        flex-wrap: wrap;
        .item {
            width: 44px;
            margin-left: 47px;
            margin-top: 20px;
            .item-top-text {
                height: 16px;
                font-size: 16px;
                // font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: 500;
                color: #ffffff;
                line-height: 24px;
                text-align: center;
            }
            .item-content {
                margin-top: 10px;
                height: 306px;
                background: rgba(255, 255, 255, 0.4);
                border-radius: 12px 12px 0px 0px;
                display: flex;
                align-self: end;
                position: relative;
                overflow: hidden;
                .bg_color_1 {
                    display: flex;
                    align-items: center;
                    align-self: end;
                    width: 44px;
                    background: linear-gradient(139deg, #029ebf 0%, #31f7d4 50%, #85ffdc 100%);
                    box-shadow: 0px 2px 10px 0px rgba(2, 47, 79, 0.38);
                    border-radius: 12px 12px 0px 0px;
                }
                .bg_color_2 {
                    display: flex;
                    align-items: center;
                    align-self: end;
                    width: 44px;
                    background: linear-gradient(153deg, #ff9018 0%, #ffa500 51%, #fef1dc 100%);
                    box-shadow: 0px 2px 10px 0px rgba(2, 47, 79, 0.38);
                    border-radius: 12px 12px 0px 0px;
                }

                .bg_color_3 {
                    display: flex;
                    align-items: center;
                    align-self: end;
                    width: 44px;
                    background: linear-gradient(153deg, #d60000 0%, #f71910 47%, #ff9d9d 100%);
                    box-shadow: 0px 2px 10px 0px rgba(2, 47, 79, 0.38);
                    border-radius: 12px 12px 0px 0px;
                }
                .item-text {
                    position: absolute;
                    top: 140px;
                    width: 44px;
                    height: 16px;
                    font-size: 16px;
                    // font-family: SourceHanSansCN, SourceHanSansCN;
                    font-weight: 500;
                    color: #ffffff;
                    line-height: 24px;
                    text-align: center;
                }
            }
        }
    }
}
.tab-bar-container {
    .tar-bar {
        position: absolute;
        right: 0;
        cursor: pointer;
        width: 482px;
        height: 96px;
        background: url("@/assets/image/wendu.png") no-repeat center center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .wendu-text {
        color: #ffffff;
        display: inline-block;
        height: 28px;
        line-height: 28px;
        .wendu-icon {
            width: 24px;
            height: 24px;
        }
        .more-text {
            position: relative;
            top: -5px;
            margin-left: 10px;
            font-size: 20px;
        }
        .big-text {
            position: relative;
            top: -5px;
            margin-top: -20px;
            margin-left: 10px;
            font-size: 28px;
        }
    }
}

.page-status {
    width: 395px;
    height: 79px;
    background: rgba(142, 205, 225, 0.5);
    border-radius: 0px 40px 40px 0px;
    position: absolute;
    left: 0;
    bottom: 20px;
    display: flex;
    align-items: center;
    .status-text {
        font-weight: 500;
        color: #ffffff;
        font-size: 16px;
        margin-left: 40px;
    }

    .status-start {
        font-weight: 500;
        color: #ffffff;
        font-size: 28px;
        margin-left: 10px;
    }
    .status-wait {
        font-weight: 500;
        font-size: 28px;
        color: #1677ff;
        margin-left: 16px;
    }
}
