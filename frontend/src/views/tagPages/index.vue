<template>
    <full-container :isSHow="false" :title="'电子标签管理'">
        <!-- 导航标签页 -->
        <div class="nav-tabs">
            <div class="nav-tab" :class="{ active: currentTab === 'device' }" @click="switchTab('device')">
                电子标签设备管理
            </div>
            <div class="nav-tab" :class="{ active: currentTab === 'data' }" @click="switchTab('data')">
                数据管理
            </div>
        </div>

        <!-- 设备管理页面 -->
        <div v-show="currentTab === 'device'">
            <div class="tab-bar-container">
                <div class="filtrate-box">
                    <div class="filtrate">
                        <div class="select-item">
                            <b>门店：</b>
                            <el-select v-model="selectedStoreId" placeholder="请选择门店" clearable class="input_258"
                                @change="handleStoreChange">
                                <el-option v-for="store in storeList" :key="store.id" :label="store.name"
                                    :value="store.id" />
                            </el-select>
                        </div>
                        <div class="select-item">
                            <b>设备状态：</b>
                            <el-select v-model="selectedStatus" placeholder="请选择状态" clearable class="input_258" multiple
                                @change="handleStatusChange">
                                <el-option label="离线" value="1" />
                                <el-option label="在线" value="2" />
                                <el-option label="已绑定" value="8" />
                                <el-option label="未绑定" value="9" />
                                <el-option label="低电量" value="5" />
                            </el-select>
                        </div>
                        <div class="select-item">
                            <b>MAC地址：</b>
                            <el-input v-model.trim="searchMac" placeholder="请输入MAC地址" clearable class="input_258"
                                @keyup.enter="getDeviceList" />
                        </div>
                    </div>
                    <el-button type="default" class="filtrate-button" @click="resetFilter">重置</el-button>
                    <el-button type="primary" class="filtrate-button" @click="getDeviceList">查询</el-button>
                </div>
                <div class="action-buttons">
                    <el-button type="primary" @click="showAddDeviceDialog">批量添加设备</el-button>
                    <el-button type="danger" @click="batchDeleteDevices"
                        :disabled="selectedDevices.length === 0">批量删除</el-button>
                    <el-button type="warning" @click="batchWakeDevices"
                        :disabled="selectedDevices.length === 0">批量唤醒</el-button>
                </div>
            </div>

            <div class="container-bg">
                <el-table :data="deviceData" border style="width: 100%" class="table"
                    @selection-change="handleSelectionChange" v-loading="loading">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" min-width="65" type="index" />
                    <el-table-column label="MAC地址" min-width="150" prop="mac" />
                    <el-table-column label="尺寸" min-width="100" prop="screenSize" :formatter="formatScreenSize" />
                    <el-table-column label="RSSI" min-width="80" prop="rssi" />
                    <el-table-column label="电量" min-width="80" prop="battery" :formatter="formatBattery" />
                    <el-table-column label="在线状态" min-width="100" prop="status" :formatter="formatStatus" />
                    <el-table-column label="最后广播时间" min-width="180" prop="lastBroadcastTime" :formatter="formatTime" />
                    <el-table-column label="备注" min-width="150" prop="remark" show-overflow-tooltip />
                    <el-table-column label="编号" min-width="120" prop="serialNumber" />
                    <el-table-column label="操作" fixed="right" width="280">
                        <template #default="scope">
                            <el-button size="small" type="primary" @click="bindData(scope.row)">绑定数据</el-button>
                            <el-button size="small" type="warning" @click="editRemark(scope.row)">编辑备注</el-button>
                            <el-button size="small" type="danger" @click="deleteDevice(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination">
                    <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize"
                        background="true" layout="total, prev, pager, next" :total="pageData.total"
                        @current-change="handlePage" />
                </div>
            </div>

            <!-- 添加设备对话框 -->
            <el-dialog v-model="addDeviceDialogVisible" title="批量添加设备" width="600px">
                <el-form :model="addDeviceForm" label-width="120px">
                    <el-form-item label="设备类型">
                        <el-radio-group v-model="addDeviceForm.type">
                            <el-radio :label="1">标签</el-radio>
                            <el-radio :label="2">大灯</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="MAC地址">
                        <el-input v-model="addDeviceForm.macText" type="textarea" :rows="6"
                            placeholder="请输入MAC地址，每行一个，例如：&#10;ac233fd01335&#10;ac233fd01336" />
                        <div class="form-tip">每行输入一个MAC地址</div>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="addDeviceDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="confirmAddDevice" :loading="addDeviceLoading">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 编辑备注对话框 -->
            <el-dialog v-model="editRemarkDialogVisible" title="编辑设备备注" width="500px">
                <el-form :model="editRemarkForm" label-width="100px">
                    <el-form-item label="MAC地址">
                        <el-input v-model="editRemarkForm.mac" disabled />
                    </el-form-item>
                    <el-form-item label="备注信息">
                        <el-input v-model="editRemarkForm.remark" placeholder="请输入备注信息" />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="editRemarkDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="confirmEditRemark" :loading="editRemarkLoading">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 绑定数据对话框 -->
            <el-dialog v-model="bindDataDialogVisible" title="绑定数据" width="800px">
                <el-form :model="bindDataForm" label-width="120px">
                    <el-form-item label="标签MAC">
                        <el-input v-model="bindDataForm.labelMac" disabled />
                    </el-form-item>
                    <el-form-item label="数据ID">
                        <el-input v-model="bindDataForm.dataId" placeholder="请输入数据ID" />
                    </el-form-item>
                    <el-form-item label="数据名称">
                        <el-input v-model="bindDataForm.dataName" placeholder="请输入数据名称" />
                    </el-form-item>
                    <el-form-item label="模板选择">
                        <el-select v-model="bindDataForm.templateId" placeholder="请选择模板" clearable>
                            <el-option v-for="template in templateList" :key="template.id" :label="template.name"
                                :value="template.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="亮灯设置">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-select v-model="bindDataForm.color" placeholder="颜色">
                                    <el-option label="关闭" :value="0" />
                                    <el-option label="蓝色" :value="1" />
                                    <el-option label="绿色" :value="2" />
                                    <el-option label="红色" :value="3" />
                                    <el-option label="黄色" :value="4" />
                                    <el-option label="白色" :value="5" />
                                    <el-option label="品红" :value="6" />
                                    <el-option label="青色" :value="7" />
                                </el-select>
                            </el-col>
                            <el-col :span="8">
                                <el-input-number v-model="bindDataForm.brightness" :min="1" :max="100"
                                    placeholder="亮度" />
                            </el-col>
                            <el-col :span="8">
                                <el-input-number v-model="bindDataForm.total" :min="1" placeholder="总时长(s)" />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="bindDataDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="confirmBindData" :loading="bindDataLoading">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>

        <!-- 数据管理页面 -->
        <div v-show="currentTab === 'data'">
            <div class="tab-bar-container">
                <div class="filtrate-box">
                    <div class="filtrate">
                        <div class="select-item">
                            <b>门店：</b>
                            <el-select v-model="dataSelectedStoreId" placeholder="请选择门店" clearable class="input_258"
                                @change="handleDataStoreChange">
                                <el-option v-for="store in storeList" :key="store.id" :label="store.name"
                                    :value="store.id" />
                            </el-select>
                        </div>
                        <div class="select-item">
                            <b>数据ID：</b>
                            <el-input v-model.trim="searchDataId" placeholder="请输入数据ID" clearable class="input_258"
                                @keyup.enter="getDataList" />
                        </div>
                        <div class="select-item">
                            <b>数据名称：</b>
                            <el-input v-model.trim="searchDataName" placeholder="请输入数据名称" clearable class="input_258"
                                @keyup.enter="getDataList" />
                        </div>
                    </div>
                    <el-button type="default" class="filtrate-button" @click="resetDataFilter">重置</el-button>
                    <el-button type="primary" class="filtrate-button" @click="getDataList">查询</el-button>
                </div>
                <div class="action-buttons">
                    <el-button type="primary" @click="showAddDataDialog">新增数据</el-button>
                    <el-button type="success" @click="showRgbSettingsDialog">RGB等设置</el-button>
                    <el-button type="danger" @click="batchDeleteDataItems"
                        :disabled="selectedDataItems.length === 0">批量删除</el-button>
                </div>
            </div>

            <div class="container-bg">
                <el-table :data="dataList" border style="width: 100%" class="table"
                    @selection-change="handleDataSelectionChange" v-loading="dataLoading">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" min-width="65" type="index" />
                    <el-table-column v-for="field in dynamicFields" :key="field.id" :label="field.name" :prop="field.id"
                        :min-width="getColumnWidth(field)" show-overflow-tooltip />
                    <el-table-column label="创建时间" min-width="180" prop="createTime" :formatter="formatTime" />
                    <el-table-column label="更新时间" min-width="180" prop="updateTime" :formatter="formatTime" />
                    <el-table-column label="操作" fixed="right" width="200">
                        <template #default="scope">
                            <el-button size="small" type="primary" @click="editDataItem(scope.row)">编辑</el-button>
                            <el-button size="small" type="warning" @click="brushDataItem(scope.row)">刷图</el-button>
                            <el-button size="small" type="danger" @click="deleteDataItem(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination">
                    <el-pagination v-model:current-page="dataPageData.page" v-model:page-size="dataPageData.pageSize"
                        background="true" layout="total, prev, pager, next" :total="dataPageData.total"
                        @current-change="handleDataPage" />
                </div>
            </div>

            <!-- 新增/编辑数据对话框 -->
            <el-dialog v-model="dataDialogVisible" :title="isEditData ? '编辑数据' : '新增数据'" width="800px">
                <el-form :model="dataForm" label-width="120px" ref="dataFormRef">
                    <el-row :gutter="20">
                        <el-col :span="12" v-for="field in dynamicFields" :key="field.id">
                            <el-form-item :label="field.name" :prop="field.id">
                                <el-input v-if="field.colunmDataType === 0" v-model="dataForm[field.id]"
                                    :placeholder="`请输入${field.name}`" />
                                <el-input-number v-else-if="field.colunmDataType === 1" v-model="dataForm[field.id]"
                                    :placeholder="`请输入${field.name}`" style="width: 100%" />
                                <el-input v-else v-model="dataForm[field.id]" :placeholder="`请输入${field.name}`" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dataDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="confirmSaveData" :loading="saveDataLoading">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- RGB设置对话框 -->
            <el-dialog v-model="rgbDialogVisible" title="RGB等设置" width="600px">
                <el-form :model="rgbForm" label-width="120px">
                    <el-form-item label="亮灯颜色">
                        <el-select v-model="rgbForm.color" placeholder="请选择颜色">
                            <el-option label="关闭" :value="0" />
                            <el-option label="蓝色" :value="1" />
                            <el-option label="绿色" :value="2" />
                            <el-option label="红色" :value="3" />
                            <el-option label="黄色" :value="4" />
                            <el-option label="白色" :value="5" />
                            <el-option label="品红" :value="6" />
                            <el-option label="青色" :value="7" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="亮度">
                        <el-slider v-model="rgbForm.brightness" :min="1" :max="100" show-input />
                    </el-form-item>
                    <el-form-item label="总时长(秒)">
                        <el-input-number v-model="rgbForm.total" :min="1" :max="3600" />
                    </el-form-item>
                    <el-form-item label="亮灯时长(毫秒)">
                        <el-input-number v-model="rgbForm.period" :min="100" :max="5000" />
                    </el-form-item>
                    <el-form-item label="灭灯时长(毫秒)">
                        <el-input-number v-model="rgbForm.interval" :min="100" :max="5000" />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="rgbDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="applyRgbSettings" :loading="rgbLoading">应用设置</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </full-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    getDeviceList as apiGetDeviceList,
    batchAddDevice,
    batchDeleteDevice,
    updateDeviceRemark,
    bindDeviceData,
    batchWakeDevice
} from '@/api/eslDevice'
import {
    getStoreList,
    getTemplateList,
    getDynamicFields,
    batchUpdateBrushLed,
    batchDeleteData as apiBatchDeleteData
} from '@/api/eslData'

// 响应式数据
const currentTab = ref('device') // 当前标签页
const loading = ref(false)
const deviceData = ref([])
const selectedDevices = ref([])
const storeList = ref([])
const templateList = ref([])

// 数据管理相关
const dataLoading = ref(false)
const dataList = ref([])
const selectedDataItems = ref([])
const dynamicFields = ref([])
const dataSelectedStoreId = ref('')
const searchDataId = ref('')
const searchDataName = ref('')

// 筛选条件
const selectedStoreId = ref('')
const selectedStatus = ref([])
const searchMac = ref('')

// 分页数据
const pageData = reactive({
    total: 0,
    page: 1,
    pageSize: 10,
})

// 数据管理分页
const dataPageData = reactive({
    total: 0,
    page: 1,
    pageSize: 10,
})

// 添加设备对话框
const addDeviceDialogVisible = ref(false)
const addDeviceLoading = ref(false)
const addDeviceForm = reactive({
    type: 1,
    macText: ''
})

// 编辑备注对话框
const editRemarkDialogVisible = ref(false)
const editRemarkLoading = ref(false)
const editRemarkForm = reactive({
    mac: '',
    remark: ''
})

// 绑定数据对话框
const bindDataDialogVisible = ref(false)
const bindDataLoading = ref(false)
const bindDataForm = reactive({
    labelMac: '',
    dataId: '',
    dataName: '',
    templateId: '',
    color: 1,
    brightness: 100,
    total: 15,
    period: 500,
    interval: 900
})

// 数据管理对话框
const dataDialogVisible = ref(false)
const saveDataLoading = ref(false)
const isEditData = ref(false)
const dataForm = ref({})
const dataFormRef = ref()

// RGB设置对话框
const rgbDialogVisible = ref(false)
const rgbLoading = ref(false)
const rgbForm = reactive({
    color: 1,
    brightness: 100,
    total: 15,
    period: 500,
    interval: 900
})

// 获取门店列表
const getStores = async () => {
    try {
        // 使用模拟数据
        const mockStores = [
            { id: '1', name: '北京朝阳店', address: '北京市朝阳区' },
            { id: '2', name: '上海浦东店', address: '上海市浦东新区' },
            { id: '3', name: '深圳南山店', address: '深圳市南山区' }
        ]
        storeList.value = mockStores

        // 实际API调用（注释掉以使用模拟数据）
        // const response = await getStoreList({ active: 1 })
        // if (response.data.code === 200) {
        //     storeList.value = response.data.data || []
        // }
    } catch (error) {
        console.error('获取门店列表失败:', error)
    }
}

// 获取模板列表
const getTemplates = async () => {
    if (!selectedStoreId.value) return
    try {
        const response = await getTemplateList({
            page: 1,
            size: 100,
            storeId: selectedStoreId.value,
            screening: 0
        })
        if (response.data.code === 200) {
            templateList.value = response.data.data?.rows || []
        }
    } catch (error) {
        console.error('获取模板列表失败:', error)
    }
}

// 获取设备列表
const getDeviceList = async () => {
    if (!selectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    loading.value = true
    try {
        const params = {
            page: pageData.page,
            size: pageData.pageSize,
            storeId: selectedStoreId.value,
            type: 1, // 1为标签
            fuzzy: searchMac.value || undefined,
            eqstatus: selectedStatus.value.length > 0 ? selectedStatus.value : undefined
        }

        // 暂时使用模拟数据进行演示
        const mockDeviceData = {
            code: 200,
            data: {
                rows: [
                    {
                        mac: 'ac233fd01335',
                        screenSize: 2,
                        rssi: -45,
                        battery: 85,
                        status: 2,
                        lastBroadcastTime: new Date().toISOString(),
                        remark: '货架A区',
                        serialNumber: 'ESL001'
                    },
                    {
                        mac: 'ac233fd01336',
                        screenSize: 3,
                        rssi: -52,
                        battery: 72,
                        status: 8,
                        lastBroadcastTime: new Date(Date.now() - 300000).toISOString(),
                        remark: '货架B区',
                        serialNumber: 'ESL002'
                    },
                    {
                        mac: 'ac233fd01337',
                        screenSize: 1,
                        rssi: -38,
                        battery: 15,
                        status: 5,
                        lastBroadcastTime: new Date(Date.now() - 600000).toISOString(),
                        remark: '货架C区',
                        serialNumber: 'ESL003'
                    }
                ],
                totalCount: 3
            }
        }

        deviceData.value = mockDeviceData.data.rows || []
        pageData.total = mockDeviceData.data.totalCount || 0

        // 实际API调用（注释掉以使用模拟数据）
        // const response = await apiGetDeviceList(params)
        // if (response.data.code === 200) {
        //     deviceData.value = response.data.data?.rows || []
        //     pageData.total = response.data.data?.totalCount || 0
        // } else {
        //     ElMessage.error(response.data.msg || '获取设备列表失败')
        // }
    } catch (error) {
        console.error('获取设备列表失败:', error)
        ElMessage.error('获取设备列表失败')
    } finally {
        loading.value = false
    }
}

// 处理门店变化
const handleStoreChange = () => {
    pageData.page = 1
    getDeviceList()
    getTemplates()
}

// 处理状态变化
const handleStatusChange = () => {
    pageData.page = 1
    getDeviceList()
}

// 重置筛选条件
const resetFilter = () => {
    selectedStatus.value = []
    searchMac.value = ''
    pageData.page = 1
    getDeviceList()
}

// 处理分页
const handlePage = (page: number) => {
    pageData.page = page
    getDeviceList()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
    selectedDevices.value = selection
}

// 显示添加设备对话框
const showAddDeviceDialog = () => {
    addDeviceForm.type = 1
    addDeviceForm.macText = ''
    addDeviceDialogVisible.value = true
}

// 确认添加设备
const confirmAddDevice = async () => {
    if (!selectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    if (!addDeviceForm.macText.trim()) {
        ElMessage.warning('请输入MAC地址')
        return
    }

    const macArray = addDeviceForm.macText.trim().split('\n').filter(mac => mac.trim())
    if (macArray.length === 0) {
        ElMessage.warning('请输入有效的MAC地址')
        return
    }

    addDeviceLoading.value = true
    try {
        const response = await batchAddDevice({
            storeId: selectedStoreId.value,
            macArray,
            type: addDeviceForm.type
        })

        if (response.data.code === 200) {
            ElMessage.success('添加设备成功')
            addDeviceDialogVisible.value = false
            getDeviceList()
        } else {
            ElMessage.error(response.data.msg || '添加设备失败')
        }
    } catch (error) {
        console.error('添加设备失败:', error)
        ElMessage.error('添加设备失败')
    } finally {
        addDeviceLoading.value = false
    }
}

// 批量删除设备
const batchDeleteDevices = async () => {
    if (selectedDevices.value.length === 0) {
        ElMessage.warning('请选择要删除的设备')
        return
    }

    try {
        await ElMessageBox.confirm('确定要删除选中的设备吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const macs = selectedDevices.value.map(device => device.mac)
        const response = await batchDeleteDevice({
            storeId: selectedStoreId.value,
            macs
        })

        if (response.data.code === 200) {
            ElMessage.success('删除设备成功')
            getDeviceList()
        } else {
            ElMessage.error(response.data.msg || '删除设备失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除设备失败:', error)
            ElMessage.error('删除设备失败')
        }
    }
}

// 批量唤醒设备
const batchWakeDevices = async () => {
    if (selectedDevices.value.length === 0) {
        ElMessage.warning('请选择要唤醒的设备')
        return
    }

    try {
        const macList = selectedDevices.value.map(device => device.mac)
        const response = await batchWakeDevice({
            macList,
            storeId: selectedStoreId.value
        })

        if (response.data.code === 200) {
            ElMessage.success('唤醒设备成功')
        } else {
            ElMessage.error(response.data.msg || '唤醒设备失败')
        }
    } catch (error) {
        console.error('唤醒设备失败:', error)
        ElMessage.error('唤醒设备失败')
    }
}

// 编辑备注
const editRemark = (row: any) => {
    editRemarkForm.mac = row.mac
    editRemarkForm.remark = row.remark || ''
    editRemarkDialogVisible.value = true
}

// 确认编辑备注
const confirmEditRemark = async () => {
    editRemarkLoading.value = true
    try {
        const response = await updateDeviceRemark({
            mac: editRemarkForm.mac,
            remark: editRemarkForm.remark,
            storeId: selectedStoreId.value
        })

        if (response.data.code === 200) {
            ElMessage.success('更新备注成功')
            editRemarkDialogVisible.value = false
            getDeviceList()
        } else {
            ElMessage.error(response.data.msg || '更新备注失败')
        }
    } catch (error) {
        console.error('更新备注失败:', error)
        ElMessage.error('更新备注失败')
    } finally {
        editRemarkLoading.value = false
    }
}

// 绑定数据
const bindData = (row: any) => {
    bindDataForm.labelMac = row.mac
    bindDataForm.dataId = ''
    bindDataForm.dataName = ''
    bindDataForm.templateId = ''
    bindDataForm.color = 1
    bindDataForm.brightness = 100
    bindDataForm.total = 15
    bindDataDialogVisible.value = true
}

// 确认绑定数据
const confirmBindData = async () => {
    if (!bindDataForm.dataId || !bindDataForm.dataName) {
        ElMessage.warning('请填写数据ID和数据名称')
        return
    }

    bindDataLoading.value = true
    try {
        const goodsMap = {
            id: bindDataForm.dataId,
            name: bindDataForm.dataName
        }

        const demoIdMap = bindDataForm.templateId ? {
            A: bindDataForm.templateId
        } : {}

        const response = await bindDeviceData({
            labelMac: bindDataForm.labelMac,
            storeId: selectedStoreId.value,
            goodsMap,
            demoIdMap,
            color: bindDataForm.color,
            brightness: bindDataForm.brightness,
            total: bindDataForm.total,
            period: bindDataForm.period,
            interval: bindDataForm.interval
        })

        if (response.data.code === 200) {
            ElMessage.success('绑定数据成功')
            bindDataDialogVisible.value = false
            getDeviceList()
        } else {
            ElMessage.error(response.data.msg || '绑定数据失败')
        }
    } catch (error) {
        console.error('绑定数据失败:', error)
        ElMessage.error('绑定数据失败')
    } finally {
        bindDataLoading.value = false
    }
}

// 删除单个设备
const deleteDevice = async (row: any) => {
    try {
        await ElMessageBox.confirm('确定要删除该设备吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const response = await batchDeleteDevice({
            storeId: selectedStoreId.value,
            macs: [row.mac]
        })

        if (response.data.code === 200) {
            ElMessage.success('删除设备成功')
            getDeviceList()
        } else {
            ElMessage.error(response.data.msg || '删除设备失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除设备失败:', error)
            ElMessage.error('删除设备失败')
        }
    }
}

// 格式化函数
const formatScreenSize = (row: any) => {
    const sizeMap = {
        1: '1.54寸',
        2: '2.13寸',
        3: '2.9寸',
        4: '4.2寸',
        5: '7.5寸'
    }
    return sizeMap[row.screenSize] || row.screenSize
}

const formatBattery = (row: any) => {
    return row.battery ? `${row.battery}%` : '-'
}

const formatStatus = (row: any) => {
    const statusMap = {
        1: '离线',
        2: '在线',
        8: '已绑定',
        9: '未绑定',
        5: '低电量'
    }
    return statusMap[row.status] || '未知'
}

const formatTime = (row: any) => {
    if (!row.lastBroadcastTime) return '-'
    return new Date(row.lastBroadcastTime).toLocaleString()
}

// 标签页切换
const switchTab = (tab: string) => {
    currentTab.value = tab
    if (tab === 'data') {
        getDynamicFieldsList()
    }
}

// 数据管理相关方法
// 获取动态字段
const getDynamicFieldsList = async () => {
    try {
        // 使用模拟动态字段数据
        const mockFields = [
            { id: 'id', name: 'ID', colunmDataType: 0 },
            { id: 'name', name: '名称', colunmDataType: 0 },
            { id: 'author', name: '作者', colunmDataType: 0 },
            { id: 'dynasty', name: '朝代', colunmDataType: 0 },
            { id: 'price', name: '价格', colunmDataType: 1 }
        ]
        dynamicFields.value = mockFields

        // 初始化表单字段
        const formFields = {}
        dynamicFields.value.forEach(field => {
            formFields[field.id] = ''
        })
        dataForm.value = formFields

        // 实际API调用（注释掉以使用模拟数据）
        // const response = await getDynamicFields()
        // if (response.data.code === 200) {
        //     dynamicFields.value = response.data.data || []
        //     // 初始化表单字段
        //     const formFields = {}
        //     dynamicFields.value.forEach(field => {
        //         formFields[field.id] = ''
        //     })
        //     dataForm.value = formFields
        // }
    } catch (error) {
        console.error('获取动态字段失败:', error)
    }
}

// 获取数据列表
const getDataList = async () => {
    if (!dataSelectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    dataLoading.value = true
    try {
        // 这里需要根据实际API调整，目前API文档中没有直接的数据列表接口
        // 暂时使用模拟数据
        const mockData = {
            code: 200,
            data: {
                rows: [
                    {
                        id: '1',
                        name: '春江花月夜',
                        author: '张若虚',
                        dynasty: '唐代',
                        price: '88.0',
                        createTime: new Date().toISOString(),
                        updateTime: new Date().toISOString()
                    },
                    {
                        id: '2',
                        name: '静夜思',
                        author: '李白',
                        dynasty: '唐代',
                        price: '66.0',
                        createTime: new Date().toISOString(),
                        updateTime: new Date().toISOString()
                    }
                ],
                totalCount: 2
            }
        }

        dataList.value = mockData.data.rows || []
        dataPageData.total = mockData.data.totalCount || 0
    } catch (error) {
        console.error('获取数据列表失败:', error)
        ElMessage.error('获取数据列表失败')
    } finally {
        dataLoading.value = false
    }
}

// 处理数据门店变化
const handleDataStoreChange = () => {
    dataPageData.page = 1
    getDataList()
}

// 重置数据筛选条件
const resetDataFilter = () => {
    searchDataId.value = ''
    searchDataName.value = ''
    dataPageData.page = 1
    getDataList()
}

// 处理数据分页
const handleDataPage = (page: number) => {
    dataPageData.page = page
    getDataList()
}

// 处理数据选择变化
const handleDataSelectionChange = (selection: any[]) => {
    selectedDataItems.value = selection
}

// 显示新增数据对话框
const showAddDataDialog = () => {
    isEditData.value = false
    // 重置表单
    const formFields = {}
    dynamicFields.value.forEach(field => {
        formFields[field.id] = ''
    })
    dataForm.value = formFields
    dataDialogVisible.value = true
}

// 编辑数据项
const editDataItem = (row: any) => {
    isEditData.value = true
    dataForm.value = { ...row }
    dataDialogVisible.value = true
}

// 确认保存数据
const confirmSaveData = async () => {
    if (!dataSelectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    saveDataLoading.value = true
    try {
        const goodsList = [dataForm.value]
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: dataSelectedStoreId.value
        })

        if (response.data.code === 200) {
            ElMessage.success(isEditData.value ? '更新数据成功' : '新增数据成功')
            dataDialogVisible.value = false
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '保存数据失败')
        }
    } catch (error) {
        console.error('保存数据失败:', error)
        ElMessage.error('保存数据失败')
    } finally {
        saveDataLoading.value = false
    }
}

// 刷图数据项
const brushDataItem = async (row: any) => {
    try {
        const goodsList = [row]
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: dataSelectedStoreId.value,
            ...rgbForm
        })

        if (response.data.code === 200) {
            ElMessage.success('刷图成功')
        } else {
            ElMessage.error(response.data.msg || '刷图失败')
        }
    } catch (error) {
        console.error('刷图失败:', error)
        ElMessage.error('刷图失败')
    }
}

// 删除数据项
const deleteDataItem = async (row: any) => {
    try {
        await ElMessageBox.confirm('确定要删除该数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const response = await apiBatchDeleteData({
            storeId: dataSelectedStoreId.value,
            idArray: [row.id]
        })

        if (response.data.code === 200) {
            ElMessage.success('删除数据成功')
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '删除数据失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除数据失败:', error)
            ElMessage.error('删除数据失败')
        }
    }
}

// 批量删除数据项
const batchDeleteDataItems = async () => {
    if (selectedDataItems.value.length === 0) {
        ElMessage.warning('请选择要删除的数据')
        return
    }

    try {
        await ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const idArray = selectedDataItems.value.map(item => item.id)
        const response = await apiBatchDeleteData({
            storeId: dataSelectedStoreId.value,
            idArray
        })

        if (response.data.code === 200) {
            ElMessage.success('批量删除数据成功')
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '批量删除数据失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除数据失败:', error)
            ElMessage.error('批量删除数据失败')
        }
    }
}

// 显示RGB设置对话框
const showRgbSettingsDialog = () => {
    rgbDialogVisible.value = true
}

// 应用RGB设置
const applyRgbSettings = async () => {
    if (selectedDataItems.value.length === 0) {
        ElMessage.warning('请先选择要应用设置的数据')
        return
    }

    rgbLoading.value = true
    try {
        const goodsList = selectedDataItems.value
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: dataSelectedStoreId.value,
            ...rgbForm
        })

        if (response.data.code === 200) {
            ElMessage.success('应用RGB设置成功')
            rgbDialogVisible.value = false
        } else {
            ElMessage.error(response.data.msg || '应用RGB设置失败')
        }
    } catch (error) {
        console.error('应用RGB设置失败:', error)
        ElMessage.error('应用RGB设置失败')
    } finally {
        rgbLoading.value = false
    }
}

// 工具函数
const getColumnWidth = (field: any) => {
    // 根据字段类型和名称长度计算列宽
    const nameLength = field.name.length
    if (nameLength <= 4) return 100
    if (nameLength <= 8) return 150
    return 200
}

// 生命周期
onMounted(() => {
    getStores()
})
</script>

<style scoped lang="scss">
@import "index.scss";

.nav-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e4e7ed;

    .nav-tab {
        padding: 12px 24px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        color: #606266;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            color: #409eff;
        }

        &.active {
            color: #409eff;
            border-bottom-color: #409eff;
            background-color: #f0f9ff;
        }
    }
}

.tab-bar-container {
    margin-bottom: 20px;

    .filtrate-box {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .filtrate {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            flex: 1;

            .select-item {
                display: flex;
                align-items: center;

                b {
                    margin-right: 8px;
                    white-space: nowrap;
                    color: #333;
                    font-weight: 500;
                }

                .input_258 {
                    width: 200px;
                }
            }
        }

        .filtrate-button {
            margin-left: 10px;
        }
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }
}

.container-bg {
    background: #fff;
    border-radius: 8px;
    padding: 20px;

    .table {
        margin-bottom: 20px;
    }

    .pagination {
        display: flex;
        justify-content: center;
    }
}

.form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

:deep(.el-table) {
    .el-table__header {
        background-color: #f5f7fa;

        th {
            background-color: #f5f7fa !important;
            color: #333;
            font-weight: 600;
        }
    }

    .el-table__row {
        &:hover {
            background-color: #f5f7fa;
        }
    }
}

:deep(.el-dialog) {
    .el-dialog__header {
        background-color: #f5f7fa;
        padding: 15px 20px;

        .el-dialog__title {
            font-weight: 600;
            color: #333;
        }
    }

    .el-dialog__body {
        padding: 20px;
    }
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
}

:deep(.el-button) {
    &.el-button--primary {
        background-color: #409eff;
        border-color: #409eff;

        &:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
        }
    }

    &.el-button--danger {
        background-color: #f56c6c;
        border-color: #f56c6c;

        &:hover {
            background-color: #f78989;
            border-color: #f78989;
        }
    }

    &.el-button--warning {
        background-color: #e6a23c;
        border-color: #e6a23c;

        &:hover {
            background-color: #ebb563;
            border-color: #ebb563;
        }
    }
}
</style>