<template>
    <full-container :isSHow="false" :title="'数据管理'">
        <div class="tab-bar-container">
            <div class="filtrate-box">
                <div class="filtrate">
                    <div class="select-item">
                        <b>门店：</b>
                        <el-select v-model="selectedStoreId" placeholder="请选择门店" clearable class="input_258"
                            @change="handleStoreChange">
                            <el-option v-for="store in storeList" :key="store.id" :label="store.name"
                                :value="store.id" />
                        </el-select>
                    </div>
                    <div class="select-item">
                        <b>数据ID：</b>
                        <el-input v-model.trim="searchDataId" placeholder="请输入数据ID" clearable class="input_258"
                            @keyup.enter="getDataList" />
                    </div>
                    <div class="select-item">
                        <b>数据名称：</b>
                        <el-input v-model.trim="searchDataName" placeholder="请输入数据名称" clearable class="input_258"
                            @keyup.enter="getDataList" />
                    </div>
                </div>
                <el-button type="default" class="filtrate-button" @click="resetFilter">重置</el-button>
                <el-button type="primary" class="filtrate-button" @click="getDataList">查询</el-button>
            </div>
            <div class="action-buttons">
                <el-button type="primary" @click="showAddDataDialog">新增数据</el-button>
                <el-button type="success" @click="showRgbSettingsDialog">RGB等设置</el-button>
                <el-button type="danger" @click="batchDeleteData" :disabled="selectedData.length === 0">批量删除</el-button>
                <el-button type="info" @click="showImportDialog">导入数据</el-button>
            </div>
        </div>

        <div class="container-bg">
            <el-table :data="dataList" border style="width: 100%" class="table"
                @selection-change="handleSelectionChange" v-loading="loading">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" min-width="65" type="index" />
                <el-table-column v-for="field in dynamicFields" :key="field.id" :label="field.name" :prop="field.id"
                    :min-width="getColumnWidth(field)" show-overflow-tooltip />
                <el-table-column label="创建时间" min-width="180" prop="createTime" :formatter="formatTime" />
                <el-table-column label="更新时间" min-width="180" prop="updateTime" :formatter="formatTime" />
                <el-table-column label="操作" fixed="right" width="200">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editData(scope.row)">编辑</el-button>
                        <el-button size="small" type="warning" @click="brushData(scope.row)">刷图</el-button>
                        <el-button size="small" type="danger" @click="deleteData(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize"
                    background="true" layout="total, prev, pager, next" :total="pageData.total"
                    @current-change="handlePage" />
            </div>
        </div>

        <!-- 新增/编辑数据对话框 -->
        <el-dialog v-model="dataDialogVisible" :title="isEdit ? '编辑数据' : '新增数据'" width="800px">
            <el-form :model="dataForm" label-width="120px" ref="dataFormRef">
                <el-row :gutter="20">
                    <el-col :span="12" v-for="field in dynamicFields" :key="field.id">
                        <el-form-item :label="field.name" :prop="field.id">
                            <el-input v-if="field.colunmDataType === 0" v-model="dataForm[field.id]"
                                :placeholder="`请输入${field.name}`" />
                            <el-input-number v-else-if="field.colunmDataType === 1" v-model="dataForm[field.id]"
                                :placeholder="`请输入${field.name}`" style="width: 100%" />
                            <el-input v-else v-model="dataForm[field.id]" :placeholder="`请输入${field.name}`" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dataDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmSaveData" :loading="saveDataLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- RGB设置对话框 -->
        <el-dialog v-model="rgbDialogVisible" title="RGB等设置" width="600px">
            <el-form :model="rgbForm" label-width="120px">
                <el-form-item label="亮灯颜色">
                    <el-select v-model="rgbForm.color" placeholder="请选择颜色">
                        <el-option label="关闭" :value="0" />
                        <el-option label="蓝色" :value="1" />
                        <el-option label="绿色" :value="2" />
                        <el-option label="红色" :value="3" />
                        <el-option label="黄色" :value="4" />
                        <el-option label="白色" :value="5" />
                        <el-option label="品红" :value="6" />
                        <el-option label="青色" :value="7" />
                    </el-select>
                </el-form-item>
                <el-form-item label="亮度">
                    <el-slider v-model="rgbForm.brightness" :min="1" :max="100" show-input />
                </el-form-item>
                <el-form-item label="总时长(秒)">
                    <el-input-number v-model="rgbForm.total" :min="1" :max="3600" />
                </el-form-item>
                <el-form-item label="亮灯时长(毫秒)">
                    <el-input-number v-model="rgbForm.period" :min="100" :max="5000" />
                </el-form-item>
                <el-form-item label="灭灯时长(毫秒)">
                    <el-input-number v-model="rgbForm.interval" :min="100" :max="5000" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="rgbDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="applyRgbSettings" :loading="rgbLoading">应用设置</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 导入数据对话框 -->
        <el-dialog v-model="importDialogVisible" title="导入数据" width="500px">
            <el-form :model="importForm" label-width="120px">
                <el-form-item label="选择门店">
                    <el-select v-model="importForm.storeIds" multiple placeholder="请选择门店">
                        <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="选择文件">
                    <el-upload ref="uploadRef" :auto-upload="false" :limit="1" accept=".xlsx,.xls,.csv"
                        :on-change="handleFileChange">
                        <el-button type="primary">选择文件</el-button>
                        <template #tip>
                            <div class="el-upload__tip">
                                只能上传 xlsx/xls/csv 文件
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="importDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmImport" :loading="importLoading">导入</el-button>
                </span>
            </template>
        </el-dialog>
    </full-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    getDynamicFields,
    batchUpdateBrushLed,
    batchDeleteData as apiBatchDeleteData,
    getDataInfo,
    importSystemData,
    getStoreList
} from '@/api/eslData'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const selectedData = ref([])
const storeList = ref([])
const dynamicFields = ref([])

// 筛选条件
const selectedStoreId = ref('')
const searchDataId = ref('')
const searchDataName = ref('')

// 分页数据
const pageData = reactive({
    total: 0,
    page: 1,
    pageSize: 10,
})

// 数据对话框
const dataDialogVisible = ref(false)
const saveDataLoading = ref(false)
const isEdit = ref(false)
const dataForm = ref({})
const dataFormRef = ref()

// RGB设置对话框
const rgbDialogVisible = ref(false)
const rgbLoading = ref(false)
const rgbForm = reactive({
    color: 1,
    brightness: 100,
    total: 15,
    period: 500,
    interval: 900
})

// 导入对话框
const importDialogVisible = ref(false)
const importLoading = ref(false)
const importForm = reactive({
    storeIds: [],
    file: null
})
const uploadRef = ref()

// 获取门店列表
const getStores = async () => {
    try {
        const response = await getStoreList({ active: 1 })
        if (response.data.code === 200) {
            storeList.value = response.data.data || []
        }
    } catch (error) {
        console.error('获取门店列表失败:', error)
    }
}

// 获取动态字段
const getDynamicFieldsList = async () => {
    try {
        const response = await getDynamicFields()
        if (response.data.code === 200) {
            dynamicFields.value = response.data.data || []
            // 初始化表单字段
            const formFields = {}
            dynamicFields.value.forEach(field => {
                formFields[field.id] = ''
            })
            dataForm.value = formFields
        }
    } catch (error) {
        console.error('获取动态字段失败:', error)
    }
}

// 获取数据列表
const getDataList = async () => {
    if (!selectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    loading.value = true
    try {
        // 这里需要根据实际API调整，目前API文档中没有直接的数据列表接口
        // 暂时使用模拟数据
        const mockData = {
            code: 200,
            data: {
                rows: [],
                totalCount: 0
            }
        }

        dataList.value = mockData.data.rows || []
        pageData.total = mockData.data.totalCount || 0
    } catch (error) {
        console.error('获取数据列表失败:', error)
        ElMessage.error('获取数据列表失败')
    } finally {
        loading.value = false
    }
}

// 处理门店变化
const handleStoreChange = () => {
    pageData.page = 1
    getDataList()
}

// 重置筛选条件
const resetFilter = () => {
    searchDataId.value = ''
    searchDataName.value = ''
    pageData.page = 1
    getDataList()
}

// 处理分页
const handlePage = (page: number) => {
    pageData.page = page
    getDataList()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
    selectedData.value = selection
}

// 显示新增数据对话框
const showAddDataDialog = () => {
    isEdit.value = false
    // 重置表单
    const formFields = {}
    dynamicFields.value.forEach(field => {
        formFields[field.id] = ''
    })
    dataForm.value = formFields
    dataDialogVisible.value = true
}

// 编辑数据
const editData = (row: any) => {
    isEdit.value = true
    dataForm.value = { ...row }
    dataDialogVisible.value = true
}

// 确认保存数据
const confirmSaveData = async () => {
    if (!selectedStoreId.value) {
        ElMessage.warning('请先选择门店')
        return
    }

    saveDataLoading.value = true
    try {
        const goodsList = [dataForm.value]
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: selectedStoreId.value
        })

        if (response.data.code === 200) {
            ElMessage.success(isEdit.value ? '更新数据成功' : '新增数据成功')
            dataDialogVisible.value = false
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '保存数据失败')
        }
    } catch (error) {
        console.error('保存数据失败:', error)
        ElMessage.error('保存数据失败')
    } finally {
        saveDataLoading.value = false
    }
}

// 刷图
const brushData = async (row: any) => {
    try {
        const goodsList = [row]
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: selectedStoreId.value,
            ...rgbForm
        })

        if (response.data.code === 200) {
            ElMessage.success('刷图成功')
        } else {
            ElMessage.error(response.data.msg || '刷图失败')
        }
    } catch (error) {
        console.error('刷图失败:', error)
        ElMessage.error('刷图失败')
    }
}

// 删除单个数据
const deleteData = async (row: any) => {
    try {
        await ElMessageBox.confirm('确定要删除该数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const response = await apiBatchDeleteData({
            storeId: selectedStoreId.value,
            idArray: [row.id]
        })

        if (response.data.code === 200) {
            ElMessage.success('删除数据成功')
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '删除数据失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除数据失败:', error)
            ElMessage.error('删除数据失败')
        }
    }
}

// 批量删除数据
const batchDeleteData = async () => {
    if (selectedData.value.length === 0) {
        ElMessage.warning('请选择要删除的数据')
        return
    }

    try {
        await ElMessageBox.confirm('确定要删除选中的数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const idArray = selectedData.value.map(item => item.id)
        const response = await apiBatchDeleteData({
            storeId: selectedStoreId.value,
            idArray
        })

        if (response.data.code === 200) {
            ElMessage.success('批量删除数据成功')
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '批量删除数据失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除数据失败:', error)
            ElMessage.error('批量删除数据失败')
        }
    }
}

// 显示RGB设置对话框
const showRgbSettingsDialog = () => {
    rgbDialogVisible.value = true
}

// 应用RGB设置
const applyRgbSettings = async () => {
    if (selectedData.value.length === 0) {
        ElMessage.warning('请先选择要应用设置的数据')
        return
    }

    rgbLoading.value = true
    try {
        const goodsList = selectedData.value
        const response = await batchUpdateBrushLed({
            goodsList,
            storeId: selectedStoreId.value,
            ...rgbForm
        })

        if (response.data.code === 200) {
            ElMessage.success('应用RGB设置成功')
            rgbDialogVisible.value = false
        } else {
            ElMessage.error(response.data.msg || '应用RGB设置失败')
        }
    } catch (error) {
        console.error('应用RGB设置失败:', error)
        ElMessage.error('应用RGB设置失败')
    } finally {
        rgbLoading.value = false
    }
}

// 显示导入对话框
const showImportDialog = () => {
    importForm.storeIds = []
    importForm.file = null
    importDialogVisible.value = true
}

// 处理文件变化
const handleFileChange = (file: any) => {
    importForm.file = file.raw
}

// 确认导入
const confirmImport = async () => {
    if (importForm.storeIds.length === 0) {
        ElMessage.warning('请选择门店')
        return
    }

    if (!importForm.file) {
        ElMessage.warning('请选择文件')
        return
    }

    importLoading.value = true
    try {
        const formData = new FormData()
        formData.append('file', importForm.file)

        const response = await importSystemData(formData, importForm.storeIds)

        if (response.data.code === 200) {
            ElMessage.success('导入数据成功')
            importDialogVisible.value = false
            getDataList()
        } else {
            ElMessage.error(response.data.msg || '导入数据失败')
        }
    } catch (error) {
        console.error('导入数据失败:', error)
        ElMessage.error('导入数据失败')
    } finally {
        importLoading.value = false
    }
}

// 工具函数
const getColumnWidth = (field: any) => {
    // 根据字段类型和名称长度计算列宽
    const nameLength = field.name.length
    if (nameLength <= 4) return 100
    if (nameLength <= 8) return 150
    return 200
}

const formatTime = (row: any, column: any, cellValue: any) => {
    if (!cellValue) return '-'
    return new Date(cellValue).toLocaleString()
}

// 生命周期
onMounted(() => {
    getStores()
    getDynamicFieldsList()
})
</script>

<style scoped lang="scss">
.tab-bar-container {
    margin-bottom: 20px;

    .filtrate-box {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .filtrate {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            flex: 1;

            .select-item {
                display: flex;
                align-items: center;

                b {
                    margin-right: 8px;
                    white-space: nowrap;
                    color: #333;
                    font-weight: 500;
                }

                .input_258 {
                    width: 200px;
                }
            }
        }

        .filtrate-button {
            margin-left: 10px;
        }
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
}

.container-bg {
    background: #fff;
    border-radius: 8px;
    padding: 20px;

    .table {
        margin-bottom: 20px;
    }

    .pagination {
        display: flex;
        justify-content: center;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

:deep(.el-table) {
    .el-table__header {
        background-color: #f5f7fa;

        th {
            background-color: #f5f7fa !important;
            color: #333;
            font-weight: 600;
        }
    }

    .el-table__row {
        &:hover {
            background-color: #f5f7fa;
        }
    }
}

:deep(.el-dialog) {
    .el-dialog__header {
        background-color: #f5f7fa;
        padding: 15px 20px;

        .el-dialog__title {
            font-weight: 600;
            color: #333;
        }
    }

    .el-dialog__body {
        padding: 20px;
    }
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
}

:deep(.el-upload__tip) {
    margin-top: 5px;
    font-size: 12px;
    color: #999;
}

:deep(.el-slider) {
    .el-slider__runway {
        background-color: #e4e7ed;
    }

    .el-slider__bar {
        background-color: #409eff;
    }

    .el-slider__button {
        border-color: #409eff;
    }
}
</style>
