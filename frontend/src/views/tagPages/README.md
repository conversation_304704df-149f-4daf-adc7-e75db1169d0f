# 电子标签管理系统

本模块包含两个主要功能页面：电子标签设备管理和数据管理。

## 功能概述

### 1. 电子标签设备管理页面

#### 主要功能
- **设备列表查询**：支持按门店、设备状态、MAC地址筛选
- **批量添加设备**：支持批量导入标签设备
- **设备信息编辑**：修改设备备注信息
- **设备绑定数据**：将设备与数据和模板进行绑定
- **设备控制**：批量删除、批量唤醒设备

#### 列表字段
- 序号
- MAC地址
- 尺寸（1.54寸、2.13寸、2.9寸、4.2寸、7.5寸）
- RSSI（信号强度）
- 电量（百分比显示）
- 在线状态（离线、在线、已绑定、未绑定、低电量）
- 最后广播时间
- 备注
- 编号

#### 操作功能
- **绑定数据**：为设备绑定数据和模板，支持亮灯设置
- **编辑备注**：修改设备备注信息
- **删除设备**：删除单个设备

### 2. 数据管理页面

#### 主要功能
- **数据列表查询**：支持按门店、数据ID、数据名称筛选
- **新增数据**：添加新的刷图数据
- **RGB等设置**：配置亮灯颜色、亮度、时长等参数
- **批量操作**：批量删除数据
- **数据导入**：支持Excel文件导入

#### 列表字段
- 序号
- 动态字段（根据系统配置的字段显示）
- 创建时间
- 更新时间

#### 操作功能
- **编辑**：修改数据信息
- **刷图**：触发数据刷新到设备
- **删除**：删除数据记录

## API接口说明

### 设备管理相关接口
- `getDeviceList`: 获取设备列表
- `batchAddDevice`: 批量添加设备
- `batchDeleteDevice`: 批量删除设备
- `updateDeviceRemark`: 更新设备备注
- `bindDeviceData`: 绑定设备数据
- `batchWakeDevice`: 批量唤醒设备

### 数据管理相关接口
- `getDynamicFields`: 获取动态字段配置
- `batchUpdateBrushLed`: 批量更新数据并刷图
- `batchDeleteData`: 批量删除数据
- `getStoreList`: 获取门店列表
- `getTemplateList`: 获取模板列表

## 使用说明

### 访问页面
1. 在路由中访问 `/tagPages/index` 进入电子标签管理主页
2. 页面顶部有两个标签页：
   - "电子标签设备管理"：设备管理功能
   - "数据管理"：数据管理功能

### 设备管理操作流程
1. **选择门店**：首先在门店下拉框中选择要管理的门店
2. **查询设备**：可以按状态、MAC地址等条件筛选设备
3. **添加设备**：点击"批量添加设备"按钮，输入MAC地址（每行一个）
4. **绑定数据**：点击设备行的"绑定数据"按钮，配置数据ID、名称、模板和亮灯参数
5. **设备控制**：选择设备后可进行批量删除或唤醒操作

### 数据管理操作流程
1. **选择门店**：首先选择要管理数据的门店
2. **查看数据**：系统会根据动态字段配置显示数据列表
3. **新增数据**：点击"新增数据"按钮，填写各字段信息
4. **RGB设置**：选择数据后点击"RGB等设置"配置亮灯参数
5. **刷图操作**：点击数据行的"刷图"按钮将数据推送到设备

## 注意事项

1. **门店选择**：所有操作都需要先选择门店
2. **权限控制**：需要有相应的API访问权限
3. **数据格式**：添加设备时MAC地址格式需要正确
4. **网络连接**：确保与云里物里服务器的网络连接正常
5. **动态字段**：数据管理页面的字段显示依赖于系统的动态字段配置

## 技术实现

- **前端框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus
- **状态管理**：Vue 3 Composition API
- **HTTP请求**：Axios
- **路由管理**：Vue Router 4

## 文件结构

```
frontend/src/views/tagPages/
├── index.vue              # 主页面（包含设备管理和数据管理）
├── dataManagement.vue     # 独立的数据管理页面（备用）
├── index.scss            # 样式文件
└── README.md             # 说明文档

frontend/src/api/
├── eslDevice.ts          # 设备管理API
└── eslData.ts           # 数据管理API
```
