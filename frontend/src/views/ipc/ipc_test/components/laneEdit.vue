<template>
  <el-dialog
    @close="close"
    v-model="dialogVisible"
    :title="title"
    close-icon="false"
    center
  >
    <el-form
      ref="form"
      :model="vaccineForm"
      :rules="rules"
      label-width="120px"
      label-position="right"
      inline
     
    >
      <el-form-item label="接种台名称">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
      <el-form-item label="1号传感器ID">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
      <el-form-item label="2号传感器ID">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
      <el-form-item label="3号传感器ID">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
      <el-form-item label="AVM触屏ID">
        <el-input v-model.trim="vaccineForm.username" placeholder="请输入" class="input_258" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer" >
        <el-button @click="dialogVisible = false" type="default">取消</el-button>
        <el-button type="primary" @click="handleConfirm(form)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>


<script lang="ts" setup>
import { FormInstance } from 'element-plus'
import { ref, reactive } from 'vue'
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const title = ref('新增接种台')
const rules = reactive({})
const vaccineList = []
const vaccineForm = reactive({
  username: '',
})

function close() {
  form.value.resetFields()
}

const showEdit = (type) => {
  dialogVisible.value = true
}

const handleConfirm = async (done: () => void) => {
  await form.value.validate((valid, fields) => {
    if (valid) {
      dialogVisible.value = false
      console.log('submit!', ruleForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}

defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';
</style>


