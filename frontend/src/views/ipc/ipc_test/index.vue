<template>
  <full-container :isSHow="false" :title="Title">
    <div class="tab-bar-container">
      <div class="tar-bar" @click="addSort('add')">
        <text class="name">新增接种台</text>
      </div>
    </div>
    <div class="container-bg">
      <el-form
        ref="form"
        :model="laneForm"
        label-width="150px"
        label-position="right"
        inline
        class="form-style"
      >
        <el-form-item label="">
          <span class="lane-num">接种台数量：4</span>
        </el-form-item>
        <el-form-item label="接种台运送顺序">
          <el-select v-model="laneForm.id" placeholder="请选择" clearable class="input_258">
            <el-option
              v-for="(item, index) in vaccineList"
              :label="item.name"
              :value="item.id"
              :key="index"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table :data="vaccineData" border style="width: 100%" class="table">
        <el-table-column label="编号" width="65" type="index" />
        <el-table-column label="接种台名称" prop="name" />
        <el-table-column label="1号传感器ID" />
        <el-table-column label="2号传感器ID" prop="name" />
        <el-table-column label="3号传感器ID" />
        <el-table-column label="AVM触屏ID" />
        <el-table-column label="分拨器开关">
          <template #default="{ row }">
            <el-switch v-model="row.status" inline-prompt active-text="开" inactive-text="关" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <span class="edit-link">编辑</span>
            <span class="del-link">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="line"></div>
      <div class="page">
        <page-ination :page="pageData" @handle-page="handlePage" />
      </div>
    </div>
    <Edit ref="edit" />
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import Edit from './components/laneEdit.vue'
const router = useRouter()
const laneForm = reactive({
  username: '',
})
const vaccineList = []
const edit = ref()
const Title = ref<String>('分拨货道参数设置')
const vaccineData = [
  {
    name: '1232',
    status: true,
  },
  {
    name: '1232',
    status: false,
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
  {
    name: '1232',
  },
]
const pageData = {
  total: 30,
  page: 1,
  page_size: 10,
}

function handlePage(page) {
  console.log(page)
  pageData.page = page
}

const addSort = (type) => {
  edit.value.showEdit(type)
}
</script>

<style lang="scss" scoped>
@import './index';
</style>
