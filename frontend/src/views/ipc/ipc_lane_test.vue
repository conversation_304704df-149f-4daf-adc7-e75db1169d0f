<template>
    <full-container :isSHow="false" :title="ipcTitle">

        <div class="container-bg">

            <div class="ipc-warp">
                <div class="ipc-avm-warp">
                    <div class="ipc-avm-title">
                        选择接种柜台
                    </div>
                    <div class="ipc-avm-select">
                        <el-radio-group v-model="avmRadio">
                            <el-radio v-for="(item, index) in avmList" :key="index" :value="item.avm_num"
                                :label="item.win_name" border />
                        </el-radio-group>
                    </div>
                </div>


                <hr />

                <div class="ipc-lane-warp">
                    <div class="ipc-lane-title">
                        选择推苗货道
                    </div>
                    <div class="ipc-lane-select">
                        <div v-for="(item, index) in laneList" :key="index" class="ipc-lane-box">
                            <div class="ipc-lane-name">
                                {{ `${laneNum[item.lane_num - 1]}货架 ` }}
                            </div>
                            <el-checkbox-group v-model="laneCheckbox">
                                <div v-for="(items, index) in item.list" :key="index" class="lane-checkbox-warp">
                                    <el-checkbox :label="items.line_name"
                                        :value='`{"lane":${items.lane_num},"line":${items.line_num}}`' border
                                        class="lane-checkbox-item" />
                                </div>
                            </el-checkbox-group>
                        </div>

                    </div>
                </div>
            </div>
            <div class="ipc-ctrl">
                <el-button @click="reset" class="ipc-button">重置</el-button>
                <el-button type="primary" class="ipc-button" @click="pushtest">推苗</el-button>
            </div>
        </div>


    </full-container>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import { indexList } from '@/api/indexList'
import { pushTest } from '@/api/ipc'
import { AvmList } from '@/api/avm'
const laneNum = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]
let avmRadio = ref("")
let laneCheckbox = ref([])
const avmList = ref([])

const laneList = ref([])

const ipcTitle = ref<String>('疫苗推送测试')

function reset() {
    avmRadio.value = ''
    laneCheckbox.value = []
}
function pushtest() {
    var laneData = []
    if (laneCheckbox.value.length == 0) {
        ElMessage.error('请选择推苗货道')
        return
    }
    if (avmRadio.value == '') {
        ElMessage.error('请选择接种柜台')
        return
    }
    laneCheckbox.value.map((item) => {
        console.log(item, "itemitemitemitem");
        let items = JSON.parse(item)
        items.laneStock = 2
        laneData.push(items)
    })

    let data = {
        freightlane: laneData,
        avm: avmRadio.value
    }
    pushTest(data).then((res) => {
        let data = res.data
        if (data.code == 0) {
            ElMessageBox.alert('推送成功', '提示', {

            })
        }
    })
}
function getLaneList() {
    indexList().then(res => {
        let data = res.data
        if (data.code == 0) {
            laneList.value = data.data
        }
    })
}

function getAvmList() {
    AvmList().then(res => {
        let data = res.data
        if (data.code == 0) {
            avmList.value = data.data.data
        }
    })
}


onMounted(() => {
    getAvmList();
    getLaneList();
})
</script>

<style lang="scss" scoped>
@import './index';


.ipc-avm-title,
.ipc-lane-title {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

.container-bg {
    background: #fff;
    height: 700px;
}

.ipc-avm-warp {
    margin-bottom: 30px;
}

.lane-checkbox-warp {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: flex-start;

    .lane-checkbox-item {
        margin-right: 10px;

    }
}

.el-checkbox-group {
    display: flex;
}

.ipc-lane-name {
    margin-right: 10px;
}

.ipc-lane-box {
    display: flex;
    margin-bottom: 20px;
}

.ipc-ctrl {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    .ipc-button {
        margin-right: 10px;
    }
}
</style>