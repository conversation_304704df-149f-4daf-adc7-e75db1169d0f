<template>
    <full-container :isSHow="false" :title="title">

        <div class="container-bg ipc-log">
            <el-table :data="Log" border style="width: 100%" class="table"
                :header-cell-style="{ height: '20px', 'line-height': '20px' }">
                <el-table-column label="序号" width="60" type="index" />
                <el-table-column label="触发类型" width="120" prop="type" />
                <el-table-column label="触发设备" width="100" prop="device_name" />
                <el-table-column label="触发内容" min-width="120" prop="content" />
                <el-table-column label="触发时间" width="210" prop="created_at" :formatter="formatTime" />

            </el-table>

            <div class="pagination">
                <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background
                    layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
            </div>
        </div>

    </full-container>
</template>


<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { timeFormat } from '@/utils/date'

import { ipcLog } from '@/api/ipc'




const title = ref('工控日志')
const Log = ref([]) // 假设从外部传入

const pageData = ref(
    {
        total: 5,
        page: 1,
        pageSize: 18,
    }
)





function handlePage(page) {
    pageData.value.page = page
    getIpcLog()
}

function getIpcLog() {
    ipcLog(pageData.value).then(res => {
        let data = res.data
        if (data.code == 0) {
            pageData.value.total = data.data.total
            Log.value = data.data.data
        }
    })
}

function formatTime(row) {


    return timeFormat(row.created_at)

}

onMounted(() => {

    getIpcLog()
})

</script>

<style lang="scss" scoped>
@import "./index.scss";


.ipc-log {
    font-size: 12px;

}

.page {
    background: #fff;
}
</style>
<style>
.el-pagination__total {
    color: #fff;
    font-weight: 600;
}
</style>