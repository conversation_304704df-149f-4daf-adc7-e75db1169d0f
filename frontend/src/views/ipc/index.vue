<template>
    <full-container :isSHow="false" :title="Title">
        <div class="container-bg">
            <div class="setting-list">
                <div v-for="(item, index) in settingList" :key="index" class="vaccine-item"
                    @click="hanleClick(item.uri)">
                    <img :src="getSrc(item.image_url)" alt="" class="icon-img" />
                    <span class="name">{{ item.name }} </span>
                </div>
            </div>
        </div>
        <Edit ref="edit" />

    </full-container>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getSrc } from "@/utils/image"
import Edit from './components/paramSet.vue'
const edit = ref()
const router = useRouter()
const Title = ref<String>('工控机相关')
const settingList = [
    {
        image_url: 'allocate.png',
        name: '疫苗推送测试',
        uri: "/ipc/ipcLaneTest"
    }
]

const hanleClick = (uri) => {

    router.push(uri)
}
</script>

<style lang="scss" scoped>
@import './index';
</style>
