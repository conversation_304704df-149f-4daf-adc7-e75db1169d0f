<template>
  <div class="transmission" @click="handleTransmission">

    <text class="name">开启传输带</text>

  </div>
</template>


<script lang="ts" setup>
import { startTransmission } from '@/api/ipc'
import { ElMessage } from 'element-plus';



function handleTransmission(parameters) {
  startTransmission()
    .then(res => {
      let data = res.data
      if (data.code == 0) {
        ElMessage({
          message: '开启传输带成功',
          type: 'success',
          duration: 1000
        })
      } else {
        ElMessage({
          message: data.message,
          type: 'error',
          duration: 1000
        })
      }
    })
}
</script>


<style lang="scss" scoped>
.transmission {
  cursor: pointer;
  padding-right: 10px;
  width: 220px;
  height: 134px;
  background: url("@/assets/image/tarbar.png") no-repeat center left;
  text-align: right;
  line-height: 134px;

  .name {
    margin-right: 14px;
    font-weight: bold;
    height: 42px;
    font-size: 32px;
    // font-family: YouSheBiaoTiHei;
    color: #f69813;
  }
}
</style>