<!-- eslint-disable vue/require-toggle-inside-transition -->
<template>
    <full-container :isSHow="true" :title="indexTitle" :bottmBarShow="false">
        <div class="home-tab">
            <div v-for="(item, index) in tabList" :key="index" class="tab-item" @click="RunCtl(item)">
                <img :src="getSrc(item.image)" alt="" class="tab-img" />
                <div class="tab-name">{{ item.name }}</div>
            </div>
        </div>

        <div class="container-bg">
            <div class="left-container">
                <img src="@/assets/image/left.png" alt="" class="left-img" />
            </div>

            <div class="right-container">
                <img src="@/assets/image/right.png" alt="" class="right-img" />
            </div>

            <!-- <div style="height: calc(100vh - 320px); width: calc(100vw - 200px)">  -->

            <div class="context-container">
                <swiper :space-between="50" :modules="modules"
                    :navigation="{ nextEl: '.right-img', prevEl: '.left-img' }" :pagination="pagination"
                    @swiper="onSwiper" @slideChange="onSlideChange">
                    <swiper-slide v-for="(item, index) in List" :key="index">
                        <div class="list-item">
                            <vaccineItem :listData="item" @edit-change="editChange" />
                        </div>
                    </swiper-slide>
                </swiper>
            </div>


            <div class="bottom-container">
                <div class="swiper-pagination-bullet page-icon swiper-pagination-bullet-active">11121</div>
            </div>

        </div>

        <div class="home-bottom">
            <div class="info-container">
                <div class="mobile-container">
                    <img src="@/assets/image/mobile.png" class="mobile-img" />
                    <text class="mobile-text">售后电话：************</text>
                </div>
                <div class="ip-contaniner">
                    <text class="ip-text">本机内网地址：{{ localIp }} </text>
                    <text> </text>
                    <text class="ip-text"> API端口:18000</text>
                </div>
                <div class="version-contaniner">
                    <text class="ip-text">服务端版本：{{ server_version }} </text>
                    <text class="ip-text">客户端版本：{{ productVersion }} </text>
                </div>
            </div>
            <div class="set-container">
                <div class="set-btn" v-for="(item, index) in btnList" :key="index">
                    <img :src="getSrc(item.image)" alt="" class="set-img" />
                    <div class="set-name">{{ item.name }}</div>
                </div>
            </div>
            <div class="ctrl-container">
                <Transmission ref="transmission" />
                <div class="home-right" @click="handleFuhe">
                    <text class="name">库存自动复核</text>
                </div>
            </div>
        </div>
        <fuhe ref="fu" @refresh="reupLoad" />
        <Msg ref="msg" @refresh="reupLoad" />

    </full-container>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive, onUnmounted, onBeforeUnmount } from "vue"
import { Swiper, SwiperSlide } from "swiper/vue"
import { Navigation, Mousewheel, Pagination } from "swiper/modules"
// 引入swiper样式，对应css 如果使用less或者css只需要把scss改为对应的即可
import "swiper/css"
import "swiper/css/navigation"
import "swiper/css/mousewheel"
import { getSrc } from "@/utils/image"
import vaccineItem from "./components/vaccineItem.vue"
import fuhe from "./components/fuheEdit.vue"
import Msg from "./components/messageEdit.vue"
import Transmission from "./components/transmission.vue"
import { useRouter } from "vue-router"
import { indexList } from "@/api/indexList"
import { laneInfoStore } from "@/store/modules/laneInfo"


const router = useRouter()
const indexTitle = localStorage.getItem("SYSTEMNAME") || "全自动疫苗智能传输系统"
const page = ref(1)
const fu = ref()
const localIp = ref("")
const server_version = ref("")
const client_version = ref("")

const msg = ref()
const transmission = ref()
const transitionName = ref("slide-left")
const totalNum = Math.ceil(100 / 30)
const LaneNum = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"]
const modules = [Mousewheel, Navigation, Pagination]
const pagination = {
    el: '.bottom-container',
    clickable: true,
    renderBullet: function (index, className) {
        return `<div class="${className}">${LaneNum[index]}柜</div>`
    },
}

let actionIndex = 0
function onSwiper(swiper) {
    console.log(swiper, "ssss")
}
function onSlideChange(e) { }

const tabList = [
    { image: "tongji.png", name: "统计", type: "uri", uri: "/home/<USER>" },
    { image: "list.png", name: "表单", type: "uri", uri: "/home/<USER>" },
    { image: "setting.png", name: "设置", type: "uri", uri: "/setting" },
    // { image: "tongji.png", name: "仪表盘", type: "uri", uri: "/dashboard" }
]

// const btnList = [
//     { image: "on.png", name: "启动" },
//     { image: "off.png", name: "关机" },
//     { image: "reload.png", name: "复位" },
//     // { image: "reload.png", name: "重启控制程序" }
// ]
var List = ref(0)

const store = laneInfoStore()

function handleFuhe() {
    fu.value.showEdit()
}

function RunCtl(item) {
    if (item.type == "uri") {
        router.push(item.uri)
    } else if (item.type == "func") {
        eval(item.func)
    }


}



function changedown() {
    if (page.value == 1) {
        page.value = totalNum
    } else {
        page.value -= 1
    }
    transitionName.value = "slide-left"
}

function changeUp() {
    if (page.value == totalNum) {
        page.value = 1
    } else {
        page.value += 1
    }
    console.log(page.value, totalNum)
    transitionName.value = "slide-right"
}

function editChange(id) {
    msg.value.showEdit(id)
}

function reupLoad() {
    getList()
}

function getList() {

    indexList().then(res => {
        let data = res.data
        if (data.code == 0) {
            List.value = data.data
        }
    })
}
const timer = ref()
onMounted(() => {
    getList()
    globalThis.timers = setInterval(() => {
        getList()
    }, 5000);


    if (typeof globalThis?.wails != "undefined") {
        import("../../../wailsjs/go/utils/Utils").then((res) => {

            res.GetLocalIp().then((res) => {
                localIp.value = res
            })
        })

    } else {
        localIp.value = "127.0.0.1"
    }

    // timer.value = setInterval(getList, 3000);

})
onUnmounted(() => {
    // clearInterval(timer.value);
});
onBeforeUnmount(() => {
    clearInterval(globalThis.timers);
})

</script>

<style scoped lang="scss">
@import "index.scss";

.info-container {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.ip-contaniner,
.version-contaniner {
    // position: absolute;
    bottom: 30px;
    left: 70px;
    color: #fff;
    font-weight: 600;
}

.ctrl-container {
    display: flex;
    flex-direction: row;
}
</style>