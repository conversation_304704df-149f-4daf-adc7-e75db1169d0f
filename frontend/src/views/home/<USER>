.container-bg {
    width: calc(100vw - 80px);
    height: calc(100vh - 240px);
    background: rgba(142, 205, 225, 0.5);
    border-radius: 24px;
    margin-top: 106px;
    overflow: hidden;

    .context-container {
        max-height: calc(100vh - 480px);
        width: calc(100vw - 200px);
        margin: 60px;
        display: flex;
        //  justify-content: space-between;
        flex-wrap: wrap;
        flex-direction: row;
        overflow: hidden;

        &::after {
            content: "";
            flex: 1;
        }

        .list-item {
            width: 100%;
            height: 80vh;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-content: flex-start;

            // justify-content: space-between;
            // align-items: flex-start;
            .item-d {
                margin-left: 10px;
                margin-bottom: 24px;
                // height: 130px;
            }
        }

        .list-A {
            width: 330px;
            height: 150px;
            background: #ffffff;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
            border-radius: 0px 12px 12px 0px;
            display: flex;

            .list-A-left {
                width: 8px;
                height: 150px;
                background: linear-gradient(135deg, #8ecde1 0%, #2c75b8 49%, #076fb0 100%);
            }

            .list-C-left {
                width: 8px;
                height: 150px;
                background: linear-gradient(153deg, #ffffff 0%, #ffa500 49%, #ff9018 100%);
            }

            .list-D-left {
                width: 8px;
                height: 150px;
                background: linear-gradient(153deg, #ff9d9d 0%, #f71910 49%, #d60000 100%);
            }

            .list-right {
                width: calc(100% - 6px);
                padding: 10px 12px;

                .list-top {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .top-text {
                        width: 48px;
                        height: 22px;
                        border-radius: 11px;
                        color: #fff;
                        line-height: 22px;
                        text-align: center;
                        font-size: 16px;
                        font-weight: 500;
                    }

                    .list-A-top-text {
                        background: #1677ff;
                    }

                    .list-C-top-text {
                        background: #ff9f18;
                    }

                    .list-D-top-text {
                        background: #ff3141;
                    }

                    .list-top-num {
                        .error-text {
                            width: 38px;
                            height: 18px;
                            font-size: 18px;
                            // font-family: SourceHanSansCN, SourceHanSansCN;
                            font-weight: 400;
                            color: #ff0000;
                            line-height: 27px;
                        }

                        .num-text {
                            width: 40px;
                            height: 18px;
                            font-size: 18px;
                            // font-family: SourceHanSansCN, SourceHanSansCN;
                            font-weight: 500;
                            line-height: 27px;
                            margin-left: 10px;
                        }

                        .num-A-text {
                            color: #1677ff;
                        }

                        .num-C-text {
                            color: #ff9f18;
                        }

                        .num-D-text {
                            color: #ff3141;
                        }
                    }
                }

                .list-A-center {
                    width: 290px;
                    height: 70px;
                    font-size: 18px;
                    // font-family: SourceHanSansCN, SourceHanSansCN;
                    font-weight: 600;
                    color: #000000;
                    line-height: 24px;
                    padding: 10px 0;
                }

                .list-A-bottom {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    .vaccine-lot {
                        width: 200px;
                        height: 20px;
                        font-size: 14px;
                        // font-family: SourceHanSansCN, SourceHanSansCN;
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.7);
                        line-height: 20px;
                    }

                    .vaccine-enterprise {
                        width: 90px;
                        height: 20px;
                        font-size: 14px;
                        text-align: right;
                        padding-right: 4px;
                        // font-family: SourceHanSansCN, SourceHanSansCN;
                        font-weight: 800;
                        color: rgba(0, 0, 0, 0.6);
                        line-height: 20px;
                    }

                    .edit-img {
                        cursor: pointer;
                        margin-top: 2px;
                        height: 16px;
                        width: 16px;
                    }
                }
            }
        }
    }

    .bottom-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 54px;

        :deep(.swiper-pagination-bullet) {
            padding: 10px 50px;
            text-wrap: nowrap;
            cursor: pointer;
            color: #fff;
            font-weight: 600;
            font-size: 20px;
            background: #0b6ab8;
            opacity: 0.35;
            border-radius: 20px;
            border: 1px solid #eeeeee;
            margin-left: 20px;
        }

        :deep(.swiper-pagination-bullet-active) {
            background: #f69813;
            opacity: 0.9;
        }
    }

    .left-container {
        position: absolute;
        left: 0;
        height: 100vh;
        width: 118px;
        transform: translateY(33%);

        .left-img {
            width: 72px;
            width: 72px;
            cursor: pointer;
        }
    }

    .right-container {
        position: absolute;
        right: 0;
        text-align: right;
        height: 100vh;
        width: 118px;
        transform: translateY(33%);

        .right-img {
            width: 72px;
            width: 72px;
            cursor: pointer;
        }
    }
}

.home-tab {
    display: flex;
    z-index: 99;
    position: absolute;
    right: 70px;
    height: 80px;
    margin-top: 14px;

    .tab-item {
        cursor: pointer;
        width: 42px;
        margin-left: 54px;

        .tab-img {
            width: 42px;
            height: 42px;
        }

        .tab-name {
            text-align: center;
            font-size: 16px;
            font-family: MicrosoftYaHei;
            color: #ffffff;
        }
    }
}

.home-bottom {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 134px;
    display: flex;
    align-self: center;
    align-items: center;
    justify-content: space-between;

    .mobile-container {
        height: 20px;
        width: 220px;
        display: flex;
        align-items: center;

        .mobile-img {
            margin-right: 4px;
            width: 16px;
            height: 16px;
        }

        .mobile-text {
            width: 200px;
            height: 21px;
            font-size: 16px;
            font-family: MicrosoftYaHei;
            color: #ffffff;
            line-height: 21px;
        }
    }

    .set-container {
        display: flex;

        .set-btn {
            margin-left: 40px;
            cursor: pointer;
            text-align: center;
            width: 120px;
            height: 78px;
            background: #ff9f18;
            box-shadow:
                0px 2px 20px 0px rgba(4, 47, 74, 0.2),
                inset 0px -3px 7px 0px rgba(255, 255, 255, 0.5);
            border-radius: 48px;

            .set-img {
                margin-top: 10px;
                width: 34px;
                height: 34px;
            }

            .set-name {
                font-size: 16px;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 500;
                color: #ffffff;
            }
        }
    }

    .home-right {
        cursor: pointer;
        margin-right: -8px;
        width: 220px;
        height: 134px;
        background: url("@/assets/image/tarbar.png") no-repeat center right;
        text-align: right;
        line-height: 134px;

        .name {
            margin-right: 14px;
            font-weight: bold;
            height: 42px;
            font-size: 32px;
            // font-family: YouSheBiaoTiHei;
            color: #f69813;
        }
    }
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
    transition: transform 0.3s;
}

.slide-right-enter-from {
    transform: translateX(0);
}

.slide-right-enter-to {
    transform: translateX(-100%);
}

.slide-right-leave-from {
    transform: translateX(0);
}

.slide-right-leave-to {
    transform: translateX(-100%);
}

.slide-left-enter-from {
    transform: translateX(-200%);
}

.slide-left-enter-to {
    transform: translateX(-100%);
}

.slide-left-leave-from {
    transform: translateX(0);
}

.slide-left-leave-to {
    transform: translateX(100%);
}