<template>
  <full-container :isSHow="false" :title="Title">
    <div class="container-bg sortingSheet-warp">
      <div style="display: flex; justify-content: space-between; width: 100%">

        <el-table :data="item.list" border style="width: 48.8%; padding-bottom: 12px" class="table"
          v-for="item in laneListData">
          <el-table-column label="货道" width="60" prop="line_name" />
          <el-table-column label="疫苗名称" min-width="280" prop="vaccine_name" />
          <el-table-column label="厂商名称" width="170" prop="enterprise_name" />
          <el-table-column label="批号" min-width="180" prop="batch_num" />
          <el-table-column label="数量" width="90" prop="stock" />
        </el-table>

      </div>
      <div class="page">
        <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.pageSize" background="true"
          layout="prev, pager, next" :total="pageData.total" @current-change="handlePage" />
      </div>
    </div>
  </full-container>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
import { indexList } from '@/api/indexList'
const Title = ref<String>('实时分拣货道库存统计表')
var pageData = {
  total: 5,
  page: 1,
  pageSize: 2,
}
const isTotal = ref<Boolean>(true)
var laneListData = ref([])
const laneList = ref([])
const formdata = ref({
  name: '',
  date: '',
})
const vaccineName = []

function handlePage(page) {

  pageData.page = page
  laneListData.value = []

  let startIdx = (page - 1) * pageData.pageSize;
  let endIdx = startIdx + pageData.pageSize;

  laneListData.value = laneList.value.slice(startIdx, endIdx);


}

function getLaneList() {
  indexList().then(res => {
    let data = res.data
    if (data.code == 0) {
      let length = data.data.length
      console.log(length, "length");

      pageData.total = length
      laneList.value = data.data
      laneListData.value = laneList.value.slice(0, 2)


    }
  })
}

onMounted(() => {

  getLaneList();
})


</script>

<style lang="scss" scoped>
@import './index';
</style>

<style lang="scss">
.sortingSheet-warp .el-table td.el-table__cell {
  height: 50px !important;
  line-height: 50px !important;
}
</style>
