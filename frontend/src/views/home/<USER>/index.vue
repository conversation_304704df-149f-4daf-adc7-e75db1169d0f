<template>
  <full-container :isSHow="false" :title="Title">
    <div class="container-bg">
      <el-form :model="formdata" inline ref="form" label-width="100px" style="margin-top: 24px">
        <el-form-item label="接种日期" style="padding-top:10px">
          <el-date-picker style="width: 258px" v-model="selecttime" type="date" placeholder="选择时间"
            value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="接种台" style="padding-top:10px">
          <el-select v-model="selectavm" placeholder="请选择" clearable class="input_258">
            <el-option v-for="(item, index) in avmList" :label="item.name" :value="item.avm_num" :key="index" />
          </el-select>
        </el-form-item>
        <el-form-item label="" style="padding-top:10px">
          <span class="vaccine-total">出苗总量：{{ total }}盒</span>
        </el-form-item>
        <el-form-item label="">
          <span class="vaccine-total">
            <el-button type="primary" @click="ReshandleQuery">重置</el-button>
            <el-button type="primary" @click="handleQuery">查询</el-button>

            <el-button type="primary" @click="exportQuery">导出查询数据</el-button>
          </span>
        </el-form-item>
      </el-form>

      <el-table :data="vaccineData" border style="width: 100%; height: 660px">
        <el-table-column label="编号" width="55" type="index" />
        <el-table-column label="接种柜台" prop="avm_num" width="100" />
        <el-table-column label="疫苗名称" prop="vaccine_name" min-width="180" />
        <el-table-column label="生产企业" prop="enterprise_name" width="150" />
        <el-table-column label="所在货道" width="90" :formatter="formatLane" />
        <el-table-column label="批号" prop="batch_num" />
        <el-table-column label="是否多人份" prop="is_multi" :formatter="formatIsmulti" width="100" />
        <el-table-column label="疫苗规格" prop="spec" min-width="120" />
        <el-table-column label="出苗数量（盒）" prop="stock" width="140" />
      </el-table>
      <div class="line"></div>
      <div class="page">
        <el-pagination v-model:current-page="pageData.page" v-model:page-size="pageData.page_size" background="true"
          layout="total, prev, pager, next" :total="pageData.total" @current-change="handlePage" />
      </div>
    </div>
  </full-container>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router'
import { AvmList } from '@/api/avm'
import { freightLaneOutHistory, exportFreightLaneOutHistory } from '@/api/lane'
const router = useRouter()
const Title = ref<String>('出苗统计表')
const selectavm = ref('')
const selecttime = ref('')
const total = ref(0)
const avmList = ref([])
import { timeFormat } from '@/utils/date'
import { ElMessageBox } from 'element-plus';
const pageData = ref({
  total: 0,
  page: 1,
  page_size: 15,
})
const vaccineData = ref([])
const formdata = reactive({
  name: '',
  date: '',
})
const vaccineName = []

function handlePage(page) {
  pageData.value.page = page
  getOutHistory()
}
function getOutHistory() {
  let data = {
    page: pageData.value.page,
    pageSize: pageData.value.page_size,
    avm_num: selectavm.value,
    time: selecttime.value
  }

  freightLaneOutHistory(data).then((res) => {
    let data = res.data
    if (data.code == 0) {
      total.value = data.data.data.pagetotal
      pageData.value.total = data.data.total
      vaccineData.value = data.data.data.list
    }

  })
}
function getAvmList() {
  AvmList().then((res) => {
    let data = res.data
    if (data.code == 0) {
      avmList.value = data.data.data
    }
  })

}
function ReshandleQuery() {
  selectavm.value = ''
  selecttime.value = ''
  getOutHistory()
}


function handleQuery() {

  getOutHistory()
}
function formatTime(row) {
  return timeFormat(row.created_at)

}


function formatLane(row) {
  let Lane = ["A", "B", "C", "D", "E", "F", "G"]
  let lane = row.lane_num
  let line = row.line_num
  return `${Lane[lane - 1]}${line}`
}
function formatIsmulti(row) {

  return row.is_multi == 1 ? '是' : '否'
}

function exportQuery() {
  let data = {
    page: pageData.value.page,
    pageSize: pageData.value.page_size,
    avm_num: selectavm.value,
    time: selecttime.value
  }
  exportFreightLaneOutHistory(data).then((res) => {
    let data = res.data
    if (data.code == 0) {
      ElMessageBox.confirm('是否导出数据', '提示', {
      })

    }
  })
}


onMounted(() => {
  getAvmList()
  handleQuery()
})

</script>

<style lang="scss" scoped>
@import './index';
</style>
