<template>
  <el-dialog @close="close" v-model="dialogVisible" title="分拣机实时库存扫描复核" style="width:690px; height: auto;"
    close-icon="false" center :close-on-click-modal="false" :close-on-press-escape="false">

    <el-form :model="fuheSelect" label-width="200px" label-position="right" inline>
      <el-form-item label="选择复核的柜号" style="width:300px;">
        <el-select v-model="fuheSelect.lane" :disabled="laserLoading">
          <el-option v-for="(item, index) in LaneList" :label="item" :value="index + 1" :key="index" />
        </el-select>
      </el-form-item>
    </el-form>
    <div v-if="streamResult" class="laserStatus">
      <div style="font-size: 18px;font-weight: 700;">激光测量状态：</div>
      <div>

        <span
          :class="['laserStatusItem', streamResult[index].status == 'success' ? 'laserStatus-success' : streamResult[index].status == 'fail' ? 'laserStatus-error' : '']"
          v-for="(item, index) in streamResult" :key="index">
          <el-tooltip class="box-item" effect="dark" :content="item.message" placement="top-start">
            {{ fuheSelect.lane != '' ? LaneList[fuheSelect.lane - 1] + item.lineNum : item.lineNum
            }}
          </el-tooltip>
        </span>
      </div>
    </div>


    <template #footer>
      <span class="dialog-footer">
        <el-button @click="Close">关闭</el-button>
        <el-button type="primary" @click="CheckLaneStock" :loading="laserLoading">自动复核</el-button>
      </span>
    </template>

  </el-dialog>
</template>


<script lang="ts" setup>

import { onMounted, ref, defineEmits } from 'vue'
import { getFreightLaneNum, checkLaneStock } from '@/api/lane'
import { ElMessage } from 'element-plus';
import { parseMultiJson } from '@/utils/streamJosn'
const emit = defineEmits(["refresh"])
const dialogVisible = ref<boolean>(false)
const LaneList = ref(["A", "B", "C", "D", "E", "F"])
const LineList = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13])
const fuheSelect = ref({
  lane: ''
})
const streamResult = ref({
  1: { laneNum: 1, lineNum: 1, message: '', status: '' },
  2: { laneNum: 1, lineNum: 2, message: '', status: '' },
  3: { laneNum: 1, lineNum: 3, message: '', status: '' },
  4: { laneNum: 1, lineNum: 4, message: '', status: '' },
  5: { laneNum: 1, lineNum: 5, message: '', status: '' },
  6: { laneNum: 1, lineNum: 6, message: '', status: '' },
  7: { laneNum: 1, lineNum: 7, message: '', status: '' },
  8: { laneNum: 1, lineNum: 8, message: '', status: '' },
  9: { laneNum: 1, lineNum: 9, message: '', status: '' },
  10: { laneNum: 1, lineNum: 10, message: '', status: '' },
  11: { laneNum: 1, lineNum: 11, message: '', status: '' },
  12: { laneNum: 1, lineNum: 12, message: '', status: '' },
  13: { laneNum: 1, lineNum: 13, message: '', status: '' },
})


const laserLoading = ref(false)
const showEdit = () => {
  dialogVisible.value = true
}

function Close() {
  dialogVisible.value = false
}
function GetFreightLaneNum() {

  getFreightLaneNum().then(res => {
    let data = res.data
    console.log(data, "data");

    if (data.code == 0) {
      LaneList.value = LaneList.value.slice(0, data.data.num)
    }
  })

}
function CheckLaneStock() {
  streamResult.value = ({
    1: { laneNum: 1, lineNum: 1, message: '', status: '' },
    2: { laneNum: 1, lineNum: 2, message: '', status: '' },
    3: { laneNum: 1, lineNum: 3, message: '', status: '' },
    4: { laneNum: 1, lineNum: 4, message: '', status: '' },
    5: { laneNum: 1, lineNum: 5, message: '', status: '' },
    6: { laneNum: 1, lineNum: 6, message: '', status: '' },
    7: { laneNum: 1, lineNum: 7, message: '', status: '' },
    8: { laneNum: 1, lineNum: 8, message: '', status: '' },
    9: { laneNum: 1, lineNum: 9, message: '', status: '' },
    10: { laneNum: 1, lineNum: 10, message: '', status: '' },
    11: { laneNum: 1, lineNum: 11, message: '', status: '' },
    12: { laneNum: 1, lineNum: 12, message: '', status: '' },
    13: { laneNum: 1, lineNum: 13, message: '', status: '' },
  })
  if (fuheSelect.value.lane <= 0) {

    ElMessage.error('请选择复核的柜架号')
    return
  }
  laserLoading.value = true
  checkLaneStock(fuheSelect.value.lane)
    .then(async response => {

      // 用来获取一个可读的流的读取器（Reader）以流的方式处理响应体数据
      const reader = response.data.getReader();
      // 将流中的字节数据解码为文本字符串

      const textDecoder = new TextDecoder();
      let result = true;


      while (result) {
        // done表示流是否已经完成读取  value包含读取到的数据块
        const { done, value } = await reader.read();
        if (done) {
          result = false;

          break;
        }

        // 拿到的value就是后端分段返回的数据，大多是以data:开头的字符串
        // 需要通过decode方法处理数据块，例如转换为文本或进行其他操作
        const chunkText = textDecoder
          .decode(value)
          .split("\n")
          .filter((val) => val.includes("data:"))
          .forEach((val) => {


            if (val.length == 0) return;
            try {
              let json = parseMultiJson(val)[0];
              console.log(val, json, "输出分段返回的数据");
              console.log(json.fail, "json.failjson.failjson.fail");

              if (json.lineNum != undefined) {
                streamResult.value[json.lineNum] = json
              }
              else if (json.fail != undefined && json.lineNum == undefined) {
                ElMessage.error(json.fail)
              } else if (json.success != undefined && json.lineNum == undefined) {
                ElMessage.success(json.success)
              }


            } catch (err) { }
          });
      }
      laserLoading.value = false
      emit("refresh")
      console.log(streamResult, '输出所有返回数据')



    })

}

onMounted(() => {
  GetFreightLaneNum()
})
defineExpose({
  showEdit,
})
</script>

<style lang="scss" scoped>
@import '../index';

.laserStatus {
  border-radius: 2px;
  // border: 1px solid #a1a1a1f3;
  line-height: 32px;
  margin-bottom: 50px;

  .laserStatusItem {
    display: inline-block;
    width: 70px;
    height: 40px;
    line-height: 40px;
    font-weight: 700;
    margin-right: 10px;
    margin-bottom: 10px;
    background: #d5d5d5f3;
    border-radius: 12px;
    border: 1px solid #d9d9d9f3;
    text-align: center;
  }

  .laserStatus-success {
    background: #19CAAD;
    color: #fff;
  }

  .laserStatus-error {
    background: #F4606C;
    color: #fff;
  }
}
</style>
