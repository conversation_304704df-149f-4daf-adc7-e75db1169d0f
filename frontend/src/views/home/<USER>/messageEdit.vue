<template>
    <el-dialog @close="close" v-model="dialogVisible" title="货道信息修改" width="880px" close-icon="false" center
        :close-on-click-modal="false">
        <el-form ref="form" :model="LaneInfo" :rules="rules" label-width="120px" label-position="right" inline
            style="height: 450px">
            <el-form-item label="货道名称">
                <el-input v-model.trim="LaneInfo.line_name" disabled class="input_258" />
            </el-form-item>

            <el-form-item label="疫苗名称">
                <el-input disabled v-model.trim="LaneInfo.vaccine_name" class="input_258" />
            </el-form-item>

            <el-form-item label="厂 家">
                <el-input disabled v-model.trim="LaneInfo.enterprise_name" class="input_258" />
            </el-form-item>

            <el-form-item label="疫苗规格">
                <el-input v-model.trim="LaneInfo.spec" disabled class="input_258" />
            </el-form-item>

            <el-form-item label="批 号">
                <el-input v-model="LaneInfo.batch_num" disabled class="input_258" />
            </el-form-item>
            <el-form-item label="货道库存">
                <el-input v-model.number="LaneInfo.stock" type="number" clearable class="input_210" />
                <span class="w_48">盒</span>
            </el-form-item>
            <el-form-item label="货道最大库存">
                <el-input v-model.trim="LaneInfo.max" disabled class="input_210" />
                <span class="w_48">盒</span>
            </el-form-item>

            <el-form-item label="单盒规格">
                <el-input v-model.trim="LaneInfo.spec_single" disabled class="input_210" />
                <span class="w_48">支/盒</span>
            </el-form-item>
        </el-form>
        <template #footer="scope">
            <span class="dialog-footer">
                <el-button size="large" @click="dialogVisible = false" type="default">取消</el-button>
                <el-button type="primary" size="large" :loading="laserLoading"
                    @click="reCheckLane(LaneInfo.lane_num, LaneInfo.line_num)">库存复核</el-button>
                <el-button size="large" type="primary" @click="handleConfirm(LaneInfo)">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { FormInstance } from "element-plus"
import { ref, reactive, onMounted, defineEmits } from "vue"
import {

    getBatchList,
    getLaneInfo,
    putLaneStock,
} from "@/api/indexList"

import { checkLineStock } from "@/api/lane"
import { ElMessage, ElMessageBox } from 'element-plus'
const form = ref<FormInstance>()
const dialogVisible = ref<boolean>(false)
const vaccineList = []
const rules = reactive({})
const laserLoading = ref(false)
const emit = defineEmits(["refresh"])

const LaneInfo = ref({
    id: 0,
    line_name: "",
    enterprise_name: "",
    spec: "",
    vaccinum_name: "",
    stock: 0,
    max: 0,
    spec_single: 0,
    batch_num: "",
    capacity: 0,
    vaccine_name: "",
})

function close() {
    form.value.resetFields()
}

const showEdit = id => {
    LaneDetail(id)
    dialogVisible.value = true
}

const handleConfirm = async (done: () => void) => {
    await form.value.validate((valid, fields) => {
        if (valid) {
            dialogVisible.value = false
            let data = {
                stock: LaneInfo.value.stock,
                line_id: LaneInfo.value.id,
                batch_num: LaneInfo.value.batch_num,
            }

            putLaneStock(data).then(res => {
                let data = res.data
                if (data.code == 0) {
                    emit("refresh")
                }
            })
        } else {
            console.log("error submit!", fields)
        }
    })
}



interface BatchItem {
    batch_num: string
}
const handleSelect = (item: any) => {
    console.log(item, "handleSelect")
}

const batchSelect = (item: any) => {
    console.log(item, "handleSelect")
}

const batchList = ref<BatchItem[]>([])

const createFilter = (queryString: string, key: string) => {
    return (restaurant: any) => {
        return restaurant[key].toLowerCase().indexOf(queryString.toLowerCase()) === 0
    }
}

const batchSearch = (queryString: string, cb: any) => {
    const results = queryString
        ? batchList.value.filter(createFilter(queryString, "batch_num"))
        : batchList.value
    cb(results)
}

function BatchList() {
    getBatchList().then(res => {
        let data = res.data
        if (data.code == 0) {
            batchList.value = data.data
        }
    })
}
function LaneDetail(id) {
    getLaneInfo(id).then(res => {
        let data = res.data
        if (data.code == 0) {
            LaneInfo.value = { ...data.data }
        }
    })
}

//库存复核

function reCheckLane(lane, line) {
    laserLoading.value = true
    var data = {
        lane: lane,
        line: line
    }
    checkLineStock(data)
        .then(res => {

            let { code, data, message } = res.data
            if (code == 0) {
                LaneInfo.value.stock = data.Stock
                ElMessage({
                    message: "测量完成，请点击确认",
                    type: "success"
                })
            } else {
                ElMessage({
                    message: message,
                    type: "error"
                })
            }


        })
        .catch(err => {

            ElMessage({
                message: err.response.data.message,
                type: 'error',
            })

        })
        .finally(() => {
            laserLoading.value = false
        })

}

onMounted(() => {
    // VaccinumNameList()
    // ManufacturerList()
    BatchList()
})
defineExpose({
    showEdit,
})
</script>

<style lang="scss" scoped>
@import "../index";
</style>
