<template>
    <div v-for="list in listData.list" class="item-d">
        <div class="list-A">
            <div :class="[
                `list-${list.stock <= 5 ? 'D' : (list.stock / list.capacity) * 100 >= 50 ? 'A' : 'C'
                }-left`,
            ]"></div>
            <div class="list-right" @click="editChange(list.id)">
                <div class="list-top">

                    <div :class="[
                        `list-${list.stock <= 5
                            ? 'D'
                            : (list.stock / list.capacity) * 100 >= 50
                                ? 'A'
                                : 'C'
                        }-top-text`,
                        'top-text',
                    ]">
                        {{ list.line_name }}
                    </div>
                    <div style="color: red;">{{ list.message }}</div>
                    <div class="list-top-num">
                        <span v-if="list.stock <= 5" class="error-text"><img src="@/assets/image/error.png"
                                style="height: 12px; width: 12px" />{{ list.stock }}盒</span>
                        <span v-if="list.stock > 5" :class="[
                            'num-text',
                            `num-${list.stock <= 5
                                ? 'D'
                                : (list.stock / list.capacity) * 100 >= 50
                                    ? 'A'
                                    : 'C'
                            }-text`,
                        ]">{{ list.stock }}盒</span>
                    </div>
                </div>
                <div class="list-A-center">
                    {{ list.vaccine_name }}
                </div>
                <div class="list-A-bottom">
                    <span class="vaccine-lot">批号：{{ list.batch_num }}</span>
                    <span class="vaccine-enterprise">{{ list.enterprise_name }}</span>
                    <img src="@/assets/image/bianji.png" class="edit-img" />
                </div>
            </div>
        </div>
    </div>
    <!--
  <div class="list-C" v-if="list.type == 'C'"> </div>

  <div class="list-D" v-if="list.type == 'D'"> </div> -->
</template>

<script lang="ts" setup>
import { watch, defineProps, defineEmits } from "vue"

const props = defineProps({
    listData: Object,
})
const emit = defineEmits(["edit-change", "refresh"])
watch(
    () => props.listData,
    (newValue, oldValue) => {
        console.log("list变化了", newValue, oldValue)
    },
    { immediate: true, deep: true },
)

function editChange(id) {
    emit("edit-change", id)
}

function reupLoad() {
    emit("refresh")
}
</script>

<style scoped lang="scss">
@import "../index";
</style>
