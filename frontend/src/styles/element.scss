.el-table__header th {
    font-weight: 600;
    color: #252525;
    background: #fafafa;
    height: 55px;
    line-height: 55px;
}

.el-table .el-table__header th {
    text-align: center;
    background: var(--el-fill-color-light) !important;
}

.el-table {
    border-radius: 8px 8px 0 0;
    background-color: #ffffff;
}

.el-table td.el-table__cell {
    text-align: center;
    font-size: 16px;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.88);
    line-height: 20px;
    height: 40px;
}

.el-dialog--center .el-dialog__footer {
    text-align: right !important;
    padding-right: 64px;
    position: absolute;
    bottom: 10px;
    right: 0;
}

// .el-button--primary {
//     width: 92px;
//     height: 40px;
//     background: #1677ff;
//     border-radius: 4px;
//     color: #ffffff !important;
//     border: none !important;
// }

// .el-button--default {
//     width: 92px;
//     height: 40px;
//     border-radius: 4px;
//     border: 1px solid rgba(0, 0, 0, 0.15);
//     color: #262626;
// }

::v-deep .el-overlay {
    background-color: rgba(0, 21, 36, 0.9) !important;
}

.el-form-item__label {
    font-size: 18px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.el-dialog--center .el-dialog__body {
    line-height: 64px;
}

:deep(.el-dialog) {
    width: 880px;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    min-height: 500px;
    transform: translate(-50%, -50%);
}

.el-switch {
    --el-switch-on-color: #1677ff;
}