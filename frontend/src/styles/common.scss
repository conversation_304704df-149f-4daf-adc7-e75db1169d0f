html,
body {
    height: 100%;
    width: 100%;
    overflow: visible;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    font-size: 14px;
}
body {
    background: #f0f2f5;
}

/* 常用 flex */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex-justify-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flex-align-center {
    display: flex;
    align-items: center;
}

/* 移动端的时候由于屏幕变小隐藏头部导航栏 */
@media screen and (max-width: 540px) {
    .app-breadcrumb {
        display: none !important;
    }
}

/** 设置滚动条 **/
::-webkit-scrollbar {
    width: 7px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: rgb(0 0 0 / 5%);
}

::-webkit-scrollbar-thumb {
    // background: rgba(0, 0, 0, 0.6);
    background-color: rgb(144 147 153 / 30%);
    // background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
    box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

/* nprogress样式 */
#nprogress .bar {
    background: $primaryColor !important;
}
#nprogress .spinner-icon {
    border-top-color: $primaryColor !important;
    border-left-color: $primaryColor !important;
}
#nprogress .peg {
    box-shadow:
        0 0 10px $primaryColor,
        0 0 5px $primaryColor !important;
}

.app-container {
    height: 100vh;
    width: 100%;
    padding: 10px 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}
.app-container-inner {
    height: 100%;
    width: 100%;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    background: white;
    padding: 20px;
    box-sizing: border-box;
}

.layout-horizontal {
    .header-icon {
        color: #bfcbd9 !important;
    }
    .el-dropdown-link {
        color: #bfcbd9 !important;
    }
}

.el-pager li:focus {
    border: none;
}
.el-dropdown:focus {
    border: none;
    outline: none !important;
}
.svg-icon:focus {
    border: none !important;
    outline: none !important;
}

.edit-link {
    width: 48px;
    height: 25px;
    font-size: 18px;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    color: #1677ff;
    line-height: 25px;
    cursor: pointer;
    margin-left: 12px;
}

.del-link {
    width: 48px;
    height: 25px;
    font-size: 18px;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    color: #ff3141;
    line-height: 25px;
    margin-left: 24px;
    cursor: pointer;
}

.input_258 {
    width: 258px;
    height: 32px;
}

.input_210 {
    width: 210px;
    height: 32px;
}

.w_48 {
    display: inline-block;
    width: 40px;
    height: 22px;
    font-size: 16px;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    color: #262626;
    line-height: 22px;
    margin-left: 8px;
    text-align: center;
}

.container-bg {
    width: calc(100vw - 80px);
    background: rgba(142, 205, 225, 0.5);
    border-radius: 24px;
}
