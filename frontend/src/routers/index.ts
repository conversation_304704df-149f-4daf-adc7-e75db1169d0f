import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
  createWebHashHistory,
  Router,
} from "vue-router"
import Layout from "@/layout/index.vue"
// 扩展继承属性
interface extendRoute {
  hidden?: boolean
}
//

// 异步组件
export const asyncRoutes = []

/**
 * path ==> 路由路径
 * name ==> 路由名称
 * component ==> 路由组件
 * redirect ==> 路由重定向
 * alwaysShow ==> 如果设置为true，将始终显示根菜单，无论其子路由长度如何
 * hidden ==> 如果“hidden:true”不会显示在侧边栏中（默认值为false）
 * keepAlive ==> 设为true 缓存
 * meta ==> 路由元信息
 * meta.title ==> 路由标题
 * meta.icon ==> 菜单icon
 * meta.affix ==> 如果设置为true将会出现在 标签栏中
 * meta.breadcrumb ==> 如果设置为false，该项将隐藏在breadcrumb中（默认值为true）
 * meta.activeMenu ==> 详情页的时候可以设置菜单高亮 ,高亮菜单的path
 */

export const constantRoutes: Array<RouteRecordRaw & extendRoute> = [
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/errorPages/404.vue"),
    hidden: true,
  },
  {
    path: "/403",
    name: "403",
    component: () => import("@/views/errorPages/403.vue"),
    hidden: true,
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    hidden: true,
    meta: { title: "登录" },
  },
  {
    path: "/",
    name: "/home",
    redirect: "/home",
    meta: { title: "首页", icon: "House" },
    hidden: true,
    children: [
      {
        path: "/home",
        component: () => import("@/views/home/<USER>"),
        name: "home",
        meta: { title: "首页", icon: "House" },
      },
      {
        path: "/home/<USER>",
        component: () => import("@/views/home/<USER>/index.vue"),
        name: "emergenceTable",
        meta: { title: "出苗统计表", keepAlive: true, icon: "" },
      },
      {
        path: "/home/<USER>",
        component: () => import("@/views/home/<USER>/index.vue"),
        name: "sortingSheet",
        meta: { title: "分拣货道库存统计表", keepAlive: true, icon: "" },
      },
    ],
  },

  // 疫苗库存设置
  {
    path: "/vaccineStock",
    redirect: "/vaccineStock/index",
    name: "vaccineStock",
    hidden: true,
    meta: {
      title: "疫苗库存设置",
      icon: "Histogram",
    },
    children: [
      {
        path: "/vaccineStock/index",
        component: () => import("@/views/vaccineStock/index.vue"),
        name: "vaccineStock",
        meta: { title: "疫苗库存设置", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/vaccineList",
        component: () => import("@/views/vaccineStock/vaccineList/index.vue"),
        name: "vaccineList",
        meta: { title: "疫苗信息", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/vaccineInfo",
        component: () => import("@/views/vaccineStock/vaccineInfo/index.vue"),
        name: "vaccineInfo",
        meta: { title: "疫苗名称管理", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/sortingParam",
        component: () => import("@/views/vaccineStock/sortingParam/index.vue"),
        name: "sortingParam",
        meta: { title: "分拣货道管理", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/vaccineManufactor",
        component: () => import("@/views/vaccineStock/vaccineManufactor/index.vue"),
        name: "vaccineManufactor",
        meta: { title: "疫苗厂商管理", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/vaccineBatch",
        component: () => import("@/views/vaccineStock/vaccineBatch/index.vue"),
        name: "vaccineBatch",
        meta: { title: "疫苗批号管理", keepAlive: true, icon: "" },
      },
      {
        path: "/vaccineStock/vaccineSku",
        component: () => import("@/views/vaccineStock/vaccineSku/index.vue"),
        name: "vaccineSku",

        meta: { title: "疫苗库存", keepAlive: true, icon: "" },
      },
    ],
  },
  {
    path: "/setting",
    redirect: "/setting/index",
    name: "setting",
    hidden: true,
    meta: {
      title: "设置",
      icon: "",
    },
    children: [
      {
        path: "/setting/index",
        component: () => import("@/views/setting/index.vue"),
        name: "setting",
        meta: { title: "设置", keepAlive: true, icon: "" },
      },
      {
        path: "/setting/laneView",
        component: () => import("@/views/setting/laneView/index.vue"),
        name: "laneView",
        meta: { title: "长条屏设置", keepAlive: true, icon: "" },
      },
      {
        path: "/setting/systemSetting",
        component: () => import("@/views/setting/systemSetting/index.vue"),
        name: "systemSetting",
        meta: { title: "系统设置", keepAlive: true, icon: "" },
      },
    ],
  },

  {
    path: "/freightLane",
    redirect: "/freightLane/index",
    name: "freightLane",
    hidden: true,
    meta: {
      title: "货道设置",
      icon: "",
    },
    children: [
      {
        path: "/freightLane/index",
        component: () => import("@/views/freightLane/index.vue"),
        name: "货道设置",
        meta: { title: "货道设置", keepAlive: true, icon: "" },
      },
      {
        path: "/freightLane/setInterval",
        component: () => import("@/views/freightLane/setInterval/index.vue"),
        name: "",
        meta: { title: "货道定时复核", keepAlive: true, icon: "" },
      },
    ],
  },
  {
    path: "/sortSetting",
    redirect: "/sortSetting/index",
    name: "sortSetting",
    hidden: true,
    meta: {
      title: "设置",
      icon: "",
    },
    children: [
      {
        path: "/sortSetting/index",
        component: () => import("@/views/sortSetting/index.vue"),
        name: "sortSetting",
        meta: { title: "设置", keepAlive: true, icon: "" },
      },
      {
        path: "/sortingLane/index",
        component: () => import("@/views/sortSetting/sortingLane/index.vue"),
        name: "sortingLane",
        meta: { title: "分拨参数设置", keepAlive: true, icon: "" },
      },
      {
        path: "/setTips/index",
        component: () => import("@/views/sortSetting/setTips/index.vue"),
        name: "setTips",
        meta: { title: "AVM多人份提示设置", keepAlive: true, icon: "" },
      },
    ],
  },
  {
    path: "/tagPages",
    redirect: "/tagPages/index",
    name: "tagPages",
    hidden: true,
    meta: {
      title: "待机",
      icon: "",
    },
    children: [
      {
        path: "/tagPages/index",
        component: () => import("@/views/tagPages/index.vue"),
        name: "tagPages",
        meta: { title: "待机", keepAlive: true, icon: "" },
      },
    ],
  },
  {
    path: "/ipc",
    redirect: "/ipc/index",
    name: "ipc",
    hidden: true,
    meta: {
      title: "工控机相关",
      icon: "",
    },
    children: [
      {
        path: "/ipc/index",
        component: () => import("@/views/ipc/index.vue"),
        name: "ipc",
        meta: { title: "工控机相关", keepAlive: true, icon: "" },
      },
      {
        path: "/ipc/ipcLaneTest",
        component: () => import("@/views/ipc/ipc_lane_test.vue"),
        name: "ipcLaneTest",
        meta: { title: "疫苗推送测试", keepAlive: true, icon: "" },
      },
      {
        path: "/ipc/log",
        component: () => import("@/views/ipc/log.vue"),
        name: "ipcLog",
        meta: { title: "工控日志", keepAlive: true, icon: "" },
      },
    ],
  },
]

/**
 * notFoundRouter(找不到路由)
 */
export const notFoundRouter = {
  path: "/:pathMatch(.*)",
  name: "notFound",
  redirect: "/404",
}

const router = createRouter({
  // history: createWebHistory(process.env.BASE_URL), // history
  history: createWebHashHistory(), // hash
  routes: constantRoutes,
})

export default router
