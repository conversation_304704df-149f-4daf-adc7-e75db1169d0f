import { defineStore } from 'pinia'
import { indexList } from '@/api/indexList'
export const laneInfoStore = defineStore({
  // id: 必须的，在所有 Store 中唯一
  id: 'laneInfo',
  // state: 返回对象的函数
  state: () => ({
    // 货道数据信息
    laneInfo: []
  }),
  getters: {},
  // 可以同步 也可以异步
  actions: {
    //  indexList
    indexList(data) {
      indexList(data).then(res => {
        let data = res.data
        if (data.code == 0) {
          console.log(data.data.data, "dddd");

          this.laneInfo = data.data.data

        }

      })
    }
  },
  // 进行持久化存储
  persist: {
    // 本地存储的名称
    key: 'laneInfo',
    //保存的位置
    storage: window.localStorage, //localstorage
  },
})
