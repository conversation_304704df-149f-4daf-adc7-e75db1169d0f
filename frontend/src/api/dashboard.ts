import request from "./request"

// 获取当日接种推送疫苗统计
export function getDailyVaccineStats() {
    return request({
        url: `/manage/dashboard/getDailyVaccineStats`,
        method: "get",
    })
}

// 获取各分拣机货道实时疫苗数量
export function getLaneVaccineStock() {
    return request({
        url: `/manage/dashboard/getLaneVaccineStock`,
        method: "get",
    })
}

// 获取当天接种台疫苗数量统计
export function getVaccinationStationStats() {
    return request({
        url: `/manage/dashboard/getVaccinationStationStats`,
        method: "get",
    })
}

// 获取疫苗类型统计
export function getVaccineTypeStats() {
    return request({
        url: `/manage/dashboard/getVaccineTypeStats`,
        method: "get",
    })
}

// 获取实时系统状态
export function getSystemStatus() {
    return request({
        url: `/manage/dashboard/getSystemStatus`,
        method: "get",
    })
}
