import request from "./request"

export function LaneDetail(id) {
    return request({
        url: `/manage/FreightLane/getLaneSingleInfo?id=${id}`,
        method: "get",
    })
}
export function LanePainList(data) {
    return request({
        url: `/manage/FreightLane/getLaneList?page=${data.page}&pageSize=${data.pageSize}`,
        method: "get",
    })
}

export function addLaneInfo(data) {
    return request({
        url: `/manage/FreightLane/addFreightLane`,
        data: data,
        method: "post",
    })
}


export function editLaneInfo(id, data) {
    return request({
        url: `/manage/FreightLane/editFreightLane/${id}`,
        data: data,
        method: "put",
    })
}

export function getFreightLaneDetail(id) {
    return request({
        url: `/manage/FreightLane/getFreightLaneDetail?id=${id}`,
        method: "get",
    })
}

export function delFreightLane(id) {
    return request({
        url: `/manage/FreightLane/deleteFreightLane?id=${id}`,
        method: "delete",
    })
}

export function onKey(data) {
    return request({
        url: `/manage/FreightLane/oneCreate`,
        data: data,
        method: "post"
    })

}

export function freightLaneOutHistory(data) {
    return request({
        url: `/manage/FreightLane/getFreightLaneOutHistory?pageSize=${data.pageSize}&page=${data.page}&avm_num=${data.avm_num}&time=${data.time}`,
        method: "get"
    })

}



export function exportFreightLaneOutHistory(data) {
    return request({
        url: `/manage/FreightLane/getFreightLaneOutHistoryExport?pageSize=${data.pageSize}&page=${data.page}&avm_num=${data.avm_num}&time=${data.time}`,
        method: "get"
    })

}




export function checkLineStock(data) {
    return request({
        url: `/manage/FreightLane/checkLineStock?lane=${data.lane}&line=${data.line}`,
        method: "get"
    })
}



export function getFreightLaneNum() {
    return request({
        url: `/manage/FreightLane/getFreightLaneNum`,
        method: "get"
    })
}

export function checkLaneStock(laneNum) {
    return request({
        url: `/manage/FreightLane/checkLaneStock?lane=${laneNum}`,
        method: "get",
        responseType: "stream"

    })
}

export function heightMeasurement(laneNum) {
    return request({
        url: `/manage/FreightLane/heightMeasurement?lane=${laneNum}`,
        method: "get",
        responseType: "stream"

    })
}