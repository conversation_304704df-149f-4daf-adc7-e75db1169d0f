import request from "./request"

export function getLaneList(data) {
    return request({
        url: `/manage/laneView/getLaneViewList?page=${data.page}&pageSize=${data.pageSize}`,
        method: "get",
    })
}


export function getLaneInfo(device_id) {
    return request({
        url: `/manage/laneView/getLaneInfo?device_id=${device_id}`,
        method: "get",
    })
}
export function editLaneInfo(id, data) {
    return request({
        url: `/manage/laneView/LaneInfo/${id}`,
        data: data,
        method: "put",
    })
}

export function deleteLane(id) {
    return request({
        url: `/manage/laneView/LaneInfo/${id}`,
        method: "delete",
    })
}