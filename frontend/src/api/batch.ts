import request from "./request"


export function getBatchList(data) {
    return request({
        url: `/manage/batch/getBatchList?page=${data.page}&pageSize=${data.pageSize}&batch_num=${data.batch_num}&vaccine_code=${data.vaccine_code}&enterprise_code=${data.enterprise_code}`,
        method: "get",
    })
}

export function getBatchDetail(id) {
    return request({
        url: `/manage/batch/getBatchDetail?id=${id}`,
        method: "get",
    })
}

export function addBatch(data) {
    return request({
        url: `/manage/batch/addBatch`,
        data: data,
        method: "post",
    })
}

export function editBatch(id, data) {
    return request({
        url: `/manage/batch/editBatch/${id}`,
        data: data,
        method: "put",
    })
}

export function deleteBatch(id) {
    return request({
        url: `/manage/batch/deleteBatch?id=${id}`,
        method: "delete",
    })
}

export function getVaccineForBatch() {
    return request({
        url: `/manage/batch/getVaccineForBatch`,
        method: "get",
    })
}

export function getManufactorForBatch(vaccine_code) {
    return request({
        url: `/manage/batch/getManufactorForBatch?vaccine_code=${vaccine_code}`,
        method: "get",
    })
}

export function getSpecForBatch(vaccine_code, enterprise_code) {
    return request({
        url: `/manage/batch/getSpecForBatch?vaccine_code=${vaccine_code}&enterprise_code=${enterprise_code}`,
        method: "get",
    })
}

export function getBatchForBatch(vaccinum_id) {
    return request({
        url: `/manage/batch/getBatchForBatch?vaccinum_id=${vaccinum_id}`,
        method: "get",
    })
}

