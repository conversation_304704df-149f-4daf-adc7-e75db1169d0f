import request from "./request"


export function getVaccinumEnterpriseList() {
    return request({
        url: `/manage/manufacturer/getManufacturer/all`,
        method: "get",
    })
}


export function getManufactorName(data) {
    return request({
        url: `/manage/manufacturer/getManufacturer?page=${data.page}&pageSize=${data.pageSize}`,
        method: "get",
    })
}

export function getManufactorNameDetail(id) {
    return request({
        url: `/manage/manufacturer/getManufacturerDetail?id=${id}`,
        method: "get",
    })
}
export function editManufactorName(id, data) {
    return request({
        url: `/manage/manufacturer/editManufacturerDetail/${id}`,
        data: data,
        method: "put",
    })
}

export function addManufactorName(data) {
    return request({
        url: `/manage/manufacturer/addManufacturerInfo`,
        data: data,
        method: "post",
    })
}

export function deleteManufactorName(id) {
    return request({
        url: `/manage/manufacturer/deleteManufacturer?id=${id}`,
        method: "delete",
    })
}
