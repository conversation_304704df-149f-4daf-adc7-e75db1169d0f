/*
 * @Author: <PERSON><PERSON><PERSON>.ho
 * @Date: 2025-02-17 00:29:04
 * @Last Modified by:   <PERSON><PERSON><PERSON>.ho
 * @Last Modified time: 2025-02-17 00:29:04
 */


import request from "./request"

export function pushTest(data) {
    return request({
        url: `/manage/ipc/pushtest`,
        data: data,
        method: "post"
    })
}

export function ipcLog(data) {
    return request({
        url: `/manage/ipc/getIpcLog?page=${data.page}&pageSize=${data.pageSize}`,
        method: "get"
    })
}

export function laserLineHeight(data) {
    return request({
        url: `/manage/ipc/laserLineHeight?lane=${data.lane_num}&line=${data.line_num}`,
        method: "get"
    })
}

export function startTransmission() {
    return request({
        url: `/manage/ipc/startTransmission`,
        method: "get"
    })
}