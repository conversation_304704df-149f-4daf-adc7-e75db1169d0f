import request from "./request"

export function getVaccinumList(data) {
    return request({
        url: `/manage/vaccin/getList?vaccine_code=${data.vaccine_code}&enterprise_code=${data.enterprise_code}&page=${data.page}&pageSize=${data.pageSize}`,
        method: "get",
    })
}

export function getVaccinumNameList() {
    return request({
        url: `/manage/vaccin/getVaccinumName/all`,
        method: "get",
    })
}

export function getVaccinumNamePaginList(data) {
    return request({
        url: `/manage/vaccin/getVaccinumName?page=${data.page}&pageSize=${data.pageSize}`,
        method: "get",
    })
}



export function getManufacturerList() {
    return request({
        url: `/manage/manufacturer/getManufacturer/all`,
        method: "get",
    })
}


export function addVaccinumInfo(data) {
    return request({
        url: `/manage/vaccin/addVaccinumInfo`,
        data: data,
        method: "post",
    })
}

export function addVaccinumNameInfo(data) {
    return request({
        url: `/manage/vaccin/addVaccinumName`,
        data: data,
        method: "post",
    })
}


export function getVaccinumDetail(id) {
    return request({
        url: `/manage/vaccin/getVaccinumDetail?id=${id}`,
        method: "get",
    })
}

export function deleteVaccinum(id) {
    return request({
        url: `/manage/vaccin/deleteVaccinum?id=${id}`,
        method: "delete",
    })
}



export function editVaccinumInfo(id, data) {
    return request({
        url: `/manage/vaccin/editVaccinumInfo/${id}`,
        data: data,
        method: "put",
    })
}

export function editVaccinumName(id, data) {
    return request({
        url: `/manage/vaccin/editVaccinumName/${id}`,
        data: data,
        method: "put",
    })
}


export function getVaccinumNameDetail(id) {
    return request({
        url: `/manage/vaccin/getVaccinumNameDetail?id=${id}`,
        method: "get",
    })
}



export function deleteVaccinumName(id) {
    return request({
        url: `/manage/vaccin/deleteVaccinumName?id=${id}`,
        method: "delete",
    })
}


export function getVaccinumfromManufacturer() {
    return request({
        url: `/manage/manufacturer/vaccinumfromManufacturer`,
        method: "get",
    })
}



export function getVaccinumfromVaccineName() {
    return request({
        url: `/manage/vaccin/vaccinumfromVaccineName`,
        method: "get",
    })
}

