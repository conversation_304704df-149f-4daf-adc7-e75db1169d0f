import request from "./request"

// 电子标签设备管理相关API

// 获取设备列表
export function getDeviceList(params: {
  page: number
  size: number
  storeId: string
  fuzzy?: string
  eqstatus?: string[]
  type: number
}) {
  return request({
    url: `/esl/label/cascadQuery`,
    method: "get",
    params
  })
}

// 批量添加设备
export function batchAddDevice(data: {
  storeId: string
  macArray: string[]
  type: number
}) {
  return request({
    url: `/esl/label/batchAdd`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 批量删除设备
export function batchDeleteDevice(data: {
  storeId: string
  macs: string[]
}) {
  return request({
    url: `/esl/label/batchDeleteLabels`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 修改标签备注信息
export function updateDeviceRemark(data: {
  mac: string
  remark: string
  storeId: string
}) {
  return request({
    url: `/esl/label/updateRemark`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 标签控制亮灯
export function controlDeviceLight(params: {
  mac: string
  storeId: string
  color: number
  total: number
  period: number
  interval: number
  brightness: number
}) {
  return request({
    url: `/esl/label/led`,
    method: "get",
    params
  })
}

// 标签绑定数据及模板
export function bindDeviceData(data: {
  labelMac: string
  storeId: string
  goodsMap: any
  demoIdMap: any
  opCode?: number
  brightness?: number
  color?: number
  interval?: number
  period?: number
  total?: number
}) {
  return request({
    url: `/esl/label/updateBindBrush`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 标签解绑
export function unbindDevice(params: {
  mac: string
  storeId: string
}) {
  return request({
    url: `/esl/label/deleteBind`,
    method: "post",
    params
  })
}

// 根据标签MAC批量查询绑定关系
export function batchQueryBindRelation(data: {
  macs: string[]
  storeId: string
}) {
  return request({
    url: `/esl/label/batchBindQuery`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 根据标签Mac批量唤醒
export function batchWakeDevice(data: {
  macList: string[]
  storeId: string
}) {
  return request({
    url: `/esl/label/batchWake?storeId=${data.storeId}`,
    method: "post",
    data: data.macList,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
