import request from "./request"

// 数据管理相关API

// 查询动态字段
export function getDynamicFields() {
  return request({
    url: `/esl/scene/findDongTaiZiDuan`,
    method: "post",
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 批量新增/修改数据、开/关灯、刷图
export function batchUpdateBrushLed(data: {
  goodsList: any[]
  storeId: string
  color?: number
  total?: number
  period?: number
  interval?: number
  brightness?: number
  opCode?: number
}) {
  return request({
    url: `/esl/goods/batchUpdateBrushLed`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 批量删除数据信息
export function batchDeleteData(data: {
  storeId: string
  idArray: string[]
}) {
  return request({
    url: `/esl/goods/batchDelete`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 获取数据信息
export function getDataInfo(params: {
  storeId: string
  id: string
}) {
  return request({
    url: `/esl/goods/singleQuery`,
    method: "get",
    params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 数据ID查询绑定标签及其所属门店
export function findDataBindInfo(params: {
  goodsId: string
}) {
  return request({
    url: `/esl/goods/findByGoodsId`,
    method: "get",
    params
  })
}

// 系统数据导入到指定区域
export function importSystemData(data: FormData, storeIdList: string[]) {
  return request({
    url: `/system/goods/import?storeIdList=${storeIdList.join(',')}`,
    method: "post",
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取用户应用场景信息（门店列表）
export function getStoreList(params: {
  active: number
  condition?: string
}) {
  return request({
    url: `/esl/store/list`,
    method: "get",
    params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 查询用户模板列表
export function getTemplateList(params: {
  page: number
  size: number
  storeId: string
  fuzzy?: string
  color?: string
  inch?: number
  screening?: number
}) {
  return request({
    url: `/esl/template/findAll`,
    method: "get",
    params
  })
}

// 查询已绑定数据的模板预览图
export function getTemplatePreview(params: {
  demoName: string
  id: string
  storeId: string
}) {
  return request({
    url: `/esl/template/preview`,
    method: "post",
    params
  })
}
