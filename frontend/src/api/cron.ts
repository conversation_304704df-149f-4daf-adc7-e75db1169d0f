import request from "./request"

export function GetCronList(data: Object) {
    return request({
        url: `/manage/cron/getCronTaskList?page=${data.page}&pageSize=${data.pagesize}`,
        method: "get",
    })
}

export function AddCron(data: Object) {
    return request({
        url: `/manage/cron/addCronTask`,
        method: "post",
        data: data
    })
}

export function EditCron(data: Object) {
    return request({
        url: `/manage/cron/editCronTask`,
        method: "put",
        data: data
    })
}

export function DeleteCron(id: Number) {
    return request({
        url: `/manage/cron/deleteCron/${id}`,
        method: "delete",
    })
}

export function ChangeCronActive(data: Object) {
    return request({
        url: `/manage/cron/changeCronActive`,
        method: "post",
        data: data
    })
}