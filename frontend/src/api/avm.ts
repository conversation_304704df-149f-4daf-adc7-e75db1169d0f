import request from "./request"
import { ChangeCronActive } from './cron';


export function AvmList(): any {
    return request({
        url: `/manage/avm/getList`,
        method: "get"
    })

}

export function AddAvm(data): any {
    return request({
        url: `/manage/avm/add`,
        method: "post",
        data: data,
    })

}

export function AddTips(data): any {
    return request({
        url: `/manage/avm/addTips`,
        method: "post",
        data: data,
    })
}

export function TipsList(): any {
    return request({
        url: `/manage/avm/tipsList`,
        method: "get"
    })
}
export function DeleteTips(id): any {
    return request({
        url: `/manage/avm/deleteTips/${id}`,
        method: "delete"
    })
}
export function ChangeTipsActive(data): any {
    return request({
        url: `/manage/avm/changeTipsActive`,
        method: "put",
        data: data
    })
}