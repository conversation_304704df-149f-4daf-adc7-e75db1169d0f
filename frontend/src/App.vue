<template>
  <el-config-provider :size="globalComSize" :locale="zhCn">
    <div @mousemove="moveEvent" @click="moveEvent">
      <router-view></router-view>
    </div>
  </el-config-provider>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useSettingStore } from '@/store/modules/setting'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { usePermissionStore } from '@/store/modules/permission'
import { useUserStore } from '@/store/modules/user'

// 配置element中文
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { useRouter } from 'vue-router'
const router = useRouter()
const SettingStore = useSettingStore()
const timmer = ref(null)
const UserStore = useUserStore()
const TagsViewStore = useTagsViewStore()
const PermissionStore = usePermissionStore()
function moveEvent() {
  // clearTimeout(timmer.value)
  // timmer.value = setTimeout(() => {
  //   // UserStore.logout()
  //   router.push({ path: '/standbyPage/index' })
  //   TagsViewStore.clearVisitedView()
  //   PermissionStore.clearRoutes()
  // }, 10000)
}

// 配置全局组件大小
const globalComSize = computed((): string => SettingStore.themeConfig.globalComSize)

// document.addEventListener('contextmenu', function (event) {
//   // 检查是否点击的是鼠标右键
//   if (event.button == 2) {
//     // 阻止默认的右键菜单弹出
//     event.preventDefault();

//     // 执行页面刷新操作
//     window.location.reload();
//   }
// });
</script>

<style lang="scss">
#app {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: "Microsoft YaHei", Avenir, sans-serif, "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-pager li:focus {
  border: none;
}

.el-dropdown:focus {
  border: none;
}

.svg-icon:focus {
  border: none;
}
</style>
