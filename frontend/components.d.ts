/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AvatarCropper: typeof import('./src/components/AvatarCropper/index.vue')['default']
    BarEcharts: typeof import('./src/components/DataScreen/barEcharts/index.vue')['default']
    BaseFormItem: typeof import('./src/components/SearchForm/components/BaseFormItem.vue')['default']
    CodeMirror: typeof import('./src/components/CodeMirror/index.vue')['default']
    CountTo: typeof import('./src/components/CountTo/index.vue')['default']
    CronForm: typeof import('./src/components/CronForm/index.vue')['default']
    Crontab: typeof import('./src/components/Crontab/index.vue')['default']
    CronTask: typeof import('./src/components/CronTask/index.vue')['default']
    Day: typeof import('./src/components/Crontab/day.vue')['default']
    EditableProTable: typeof import('./src/components/Table/EditableProTable/index.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FullContainer: typeof import('./src/components/FullContainer/index.vue')['default']
    Hour: typeof import('./src/components/Crontab/hour.vue')['default']
    IndexAll: typeof import('./src/components/CronForm/index-all.vue')['default']
    LineEcharts: typeof import('./src/components/DataScreen/lineEcharts/index.vue')['default']
    MigrationEcharts: typeof import('./src/components/DataScreen/migrationEcharts/index.vue')['default']
    Min: typeof import('./src/components/Crontab/min.vue')['default']
    Month: typeof import('./src/components/Crontab/month.vue')['default']
    Multiline: typeof import('./src/components/DataScreen/Multiline/index.vue')['default']
    Pageination: typeof import('./src/components/pageination/index.vue')['default']
    PageWrapLayout: typeof import('./src/components/PageWrapLayout/index.vue')['default']
    Pie: typeof import('./src/components/DataScreen/pie/index.vue')['default']
    Pipeline: typeof import('./src/components/pipeline/index.vue')['default']
    PropTable: typeof import('./src/components/Table/PropTable/index.vue')['default']
    Result: typeof import('./src/components/Crontab/result.vue')['default']
    RightClickMenu: typeof import('./src/components/RightClickMenu/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./src/components/SearchForm/index.vue')['default']
    Second: typeof import('./src/components/Crontab/second.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon/index.vue')['default']
    SwitchDark: typeof import('./src/components/SwitchDark/index.vue')['default']
    Theme: typeof import('./src/components/Theme/index.vue')['default']
    UContainerLayout: typeof import('./src/components/u-container-layout/index.vue')['default']
    Upload: typeof import('./src/components/Upload/index.vue')['default']
    WangEdior: typeof import('./src/components/WangEdior/index.vue')['default']
    Week: typeof import('./src/components/Crontab/week.vue')['default']
    Year: typeof import('./src/components/Crontab/year.vue')['default']
    ZbPipelineStart: typeof import('./src/components/pipeline/zb-pipeline-start.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
