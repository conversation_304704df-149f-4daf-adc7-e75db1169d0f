package main

import (
	"archive/zip"
	"io"
	"net/http"
	"os"
	"path/filepath"

	go_logger "github.com/phachon/go-logger"
)

const (
	UpdateURL = "http://example.com/update.zip"
)

func UpdateApp() {
	logger := go_logger.NewLogger()
	// 下载更新文件
	err := downloadUpdate(UpdateURL, "update.zip")
	if err != nil {

		logger.Errorf("Failed to download update: %v", err)
		return
	}

	// 应用更新
	err = applyUpdate("update.zip")
	if err != nil {
		logger.Errorf("Failed to apply update: %v", err)
		return
	}

	logger.Info("Update applied successfully!")
}

func downloadUpdate(url, destination string) error {
	// 创建文件
	file, err := os.Create(destination)
	if err != nil {
		return err
	}
	defer file.Close()

	// 下载更新文件
	response, err := http.Get(url)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	// 写入文件
	_, err = io.Copy(file, response.Body)
	if err != nil {
		return err
	}

	return nil
}

func applyUpdate(updateFile string) error {
	// 解压更新文件
	err := unzip(updateFile, "./temp")
	if err != nil {
		return err
	}

	// 替换旧文件
	err = replaceFiles("./temp", "./")
	if err != nil {
		return err
	}

	// 清理临时文件
	err = os.RemoveAll("./temp")
	if err != nil {
		return err
	}

	return nil
}

func unzip(src, dest string) error {
	// 打开更新文件
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	// 解压更新文件中的内容到目标目录
	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer rc.Close()

		// 创建目标文件
		path := filepath.Join(dest, f.Name)
		if f.FileInfo().IsDir() {
			os.MkdirAll(path, f.Mode())
		} else {
			// 创建父目录
			if err = os.MkdirAll(filepath.Dir(path), os.ModePerm); err != nil {
				return err
			}

			// 创建并写入文件
			outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return err
			}
			defer outFile.Close()

			_, err = io.Copy(outFile, rc)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func replaceFiles(src, dest string) error {
	// 遍历更新文件夹中的文件
	err := filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 构建目标文件路径
		destPath := filepath.Join(dest, path[len(src):])

		// 如果是目录，创建目录
		if info.IsDir() {
			err = os.MkdirAll(destPath, info.Mode())
			if err != nil {
				return err
			}
		} else {
			// 如果是文件，替换文件
			err = os.Rename(path, destPath)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}
